# AI新闻分析系统 - 部署指南

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [详细部署步骤](#详细部署步骤)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [故障排除](#故障排除)
- [生产环境部署](#生产环境部署)

## 🔧 系统要求

### 最低要求
- **操作系统**: macOS 10.15+ 或 Ubuntu 18.04+
- **Python**: 3.8+ (推荐 3.11+)
- **Node.js**: 18.0+ (推荐 20.0+)
- **内存**: 4GB RAM (推荐 8GB+)
- **存储**: 2GB 可用空间

### 必需工具
- `git` - 版本控制
- `curl` - 网络请求工具
- `lsof` - 端口检查工具

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd cash-flow
```

### 2. 一键环境部署
```bash
./setup_environment.sh
```

### 3. 启动系统
```bash
./start_system.sh
```

### 4. 访问应用
- **前端界面**: http://localhost:3000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 📖 详细部署步骤

### 步骤 1: 环境准备

#### macOS 系统
```bash
# 安装 Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装必要工具
brew install python@3.11 node git curl
```

#### Ubuntu/Debian 系统
```bash
# 更新包管理器
sudo apt update

# 安装必要工具
sudo apt install python3 python3-pip nodejs npm git curl lsof -y

# 安装最新版 Node.js (可选)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 步骤 2: 项目部署

#### 2.1 获取项目代码
```bash
git clone <repository-url>
cd cash-flow
```

#### 2.2 运行环境部署脚本
```bash
# 赋予执行权限
chmod +x setup_environment.sh

# 运行部署脚本
./setup_environment.sh
```

脚本将自动执行以下操作：
- ✅ 检查系统环境
- ✅ 安装 uv 包管理器
- ✅ 创建 Python 虚拟环境
- ✅ 安装 Python 依赖
- ✅ 安装前端依赖
- ✅ 配置环境变量
- ✅ 创建必要目录

#### 2.3 配置 API 密钥

在运行部署脚本时，您需要提供以下信息：

**必需配置**:
- `Gemini API Key`: 用于AI分析功能
  - 获取地址: https://makersuite.google.com/app/apikey

**可选配置**:
- `Tushare Token`: 用于股票数据 (可选)
  - 获取地址: https://tushare.pro/register
- `GLM API Key`: 用于智谱AI (可选)
  - 获取地址: https://open.bigmodel.cn/

### 步骤 3: 启动服务

#### 3.1 开发模式启动
```bash
./start_system.sh
```

#### 3.2 生产模式启动
```bash
./start_system.sh --mode production
```

#### 3.3 自定义端口启动
```bash
./start_system.sh --backend-port 8080 --frontend-port 3001
```

## ⚙️ 配置说明

### 环境变量文件 (.env)

```bash
# AI模型配置
GEMINI_API_KEY=your_gemini_api_key
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta
GEMINI_MODEL=gemini-2.5-flash-lite-preview-06-17

# 智谱AI配置 (可选)
GLM_API_KEY=your_glm_api_key
GLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

# 数据源配置 (可选)
TUSHARE_TOKEN=your_tushare_token

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
FRONTEND_PORT=3000

# 环境配置
NODE_ENV=development
LOG_LEVEL=INFO
```

### 目录结构

```
cash-flow/
├── backend/                 # 后端代码
├── frontend/               # 前端代码
├── data/                   # 数据存储
├── logs/                   # 日志文件
├── .venv/                  # Python虚拟环境
├── .env                    # 环境变量
├── setup_environment.sh    # 环境部署脚本
├── start_system.sh        # 启动脚本
├── stop_system.sh         # 停止脚本
└── restart_system.sh      # 重启脚本
```

## 🛠️ 服务管理

### 启动服务
```bash
# 开发模式
./start_system.sh

# 生产模式
./start_system.sh --mode production

# 查看帮助
./start_system.sh --help
```

### 停止服务
```bash
./stop_system.sh
```

### 重启服务
```bash
./restart_system.sh

# 重启为生产模式
./restart_system.sh --mode production
```

### 查看日志
```bash
# 查看后端日志
tail -f logs/backend.log

# 查看前端日志
tail -f logs/frontend.log

# 查看实时日志
./start_system.sh  # 开发模式会自动显示日志
```

### 检查服务状态
```bash
# 检查进程
ps aux | grep -E 'uvicorn|next'

# 检查端口
lsof -i:8000 -i:3000

# 健康检查
curl http://localhost:8000/health
curl http://localhost:3000
```

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
**错误**: `Address already in use`

**解决方案**:
```bash
# 查看端口占用
lsof -i:8000
lsof -i:3000

# 停止占用进程
./stop_system.sh

# 或手动停止
kill -9 <PID>
```

#### 2. Python 依赖问题
**错误**: `ModuleNotFoundError`

**解决方案**:
```bash
# 重新安装依赖
source .venv/bin/activate
uv pip install -r requirements.txt
uv pip install -r backend/requirements.txt
```

#### 3. 前端依赖问题
**错误**: `Module not found`

**解决方案**:
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
cd ..
```

#### 4. API 密钥问题
**错误**: `API key not found` 或 `Unauthorized`

**解决方案**:
```bash
# 检查环境变量
cat .env | grep API_KEY

# 重新配置
./setup_environment.sh
```

#### 5. 权限问题
**错误**: `Permission denied`

**解决方案**:
```bash
# 赋予脚本执行权限
chmod +x *.sh

# 检查文件权限
ls -la *.sh
```

### 日志分析

#### 后端日志位置
- 文件: `logs/backend.log`
- 常见错误关键词: `ERROR`, `CRITICAL`, `Exception`

#### 前端日志位置
- 文件: `logs/frontend.log`
- 常见错误关键词: `Error`, `Failed`, `Cannot`

#### 调试模式
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
./start_system.sh
```

### 性能优化

#### 1. 内存不足
```bash
# 检查内存使用
free -h  # Linux
vm_stat  # macOS

# 减少并发数 (生产模式)
# 编辑 start_system.sh，减少 --workers 数量
```

#### 2. 磁盘空间不足
```bash
# 清理日志
rm -f logs/*.log

# 清理临时文件
rm -rf temp/*

# 清理Python缓存
find . -name "__pycache__" -exec rm -rf {} +
```

## 🏭 生产环境部署

### 1. 系统配置

#### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 8000
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

#### 系统服务配置
创建 systemd 服务文件:

```bash
# 创建服务文件
sudo nano /etc/systemd/system/ai-news-analysis.service
```

服务文件内容:
```ini
[Unit]
Description=AI News Analysis System
After=network.target

[Service]
Type=forking
User=your-user
WorkingDirectory=/path/to/cash-flow
ExecStart=/path/to/cash-flow/start_system.sh --mode production
ExecStop=/path/to/cash-flow/stop_system.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务:
```bash
sudo systemctl daemon-reload
sudo systemctl enable ai-news-analysis
sudo systemctl start ai-news-analysis
```

### 2. 反向代理配置 (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 后端API
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. SSL 证书配置

```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 4. 监控和日志

#### 日志轮转
```bash
# 创建 logrotate 配置
sudo nano /etc/logrotate.d/ai-news-analysis
```

配置内容:
```
/path/to/cash-flow/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 your-user your-group
    postrotate
        systemctl reload ai-news-analysis
    endscript
}
```

## 📞 支持

如果您在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查日志文件中的错误信息
3. 确认所有系统要求都已满足
4. 验证 API 密钥配置正确

## 🔄 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新部署环境 (如有必要)
./setup_environment.sh

# 重启服务
./restart_system.sh
```
