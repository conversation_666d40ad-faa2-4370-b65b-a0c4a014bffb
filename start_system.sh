#!/bin/bash
# AI新闻分析系统 - 智能启动脚本
# 支持开发和生产环境，增强错误处理和监控

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 默认配置
MODE="development"
BACKEND_PORT=8000
FRONTEND_PORT=3000
LOG_DIR="logs"
BACKEND_LOG="$LOG_DIR/backend.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mode|-m)
                MODE="$2"
                shift 2
                ;;
            --backend-port)
                BACKEND_PORT="$2"
                shift 2
                ;;
            --frontend-port)
                FRONTEND_PORT="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    echo "AI新闻分析系统启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --mode MODE          运行模式 (development|production) [默认: development]"
    echo "  --backend-port PORT      后端端口 [默认: 8000]"
    echo "  --frontend-port PORT     前端端口 [默认: 3000]"
    echo "  -h, --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                      # 开发模式启动"
    echo "  $0 --mode production    # 生产模式启动"
    echo "  $0 --backend-port 8080  # 自定义后端端口"
}

# 检查系统要求
check_prerequisites() {
    log_info "检查系统要求..."
    
    # 检查是否在项目根目录
    if [ ! -f "pyproject.toml" ] && [ ! -f "package.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        log_error "未找到.env文件，请先运行 ./setup_environment.sh"
        exit 1
    fi
    
    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        log_error "未找到Python虚拟环境，请先运行 ./setup_environment.sh"
        exit 1
    fi
    
    # 检查前端依赖
    if [ -d "frontend" ] && [ ! -d "frontend/node_modules" ]; then
        log_error "前端依赖未安装，请先运行 ./setup_environment.sh"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 加载环境变量
load_environment() {
    log_info "加载环境变量..."
    
    set -a  # 自动导出变量
    source .env
    set +a
    
    # 验证必要的环境变量
    if [ -z "$GEMINI_API_KEY" ]; then
        log_error "GEMINI_API_KEY未设置，请检查.env文件"
        exit 1
    fi
    
    log_success "环境变量加载完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    local dirs=("$LOG_DIR" "data" "temp")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
        fi
    done
}

# 初始化数据库
initialize_databases() {
    log_info "初始化数据库..."

    # 激活虚拟环境
    if [ -d ".venv" ]; then
        source .venv/bin/activate
    fi

    # 检查数据库初始化器是否存在
    if [ -f "backend/database_initializer.py" ]; then
        log_info "执行数据库初始化..."

        # 运行数据库初始化器
        if python backend/database_initializer.py; then
            log_success "数据库初始化完成"
        else
            log_warning "数据库初始化失败，但继续启动服务"
        fi
    else
        log_warning "未找到数据库初始化器，跳过数据库初始化"
    fi
}

# 检查端口占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -ti:$port > /dev/null 2>&1; then
        log_warning "端口 $port 已被占用，正在停止占用该端口的进程..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
        
        if lsof -ti:$port > /dev/null 2>&1; then
            log_error "无法释放端口 $port，请手动检查并停止占用该端口的进程"
            echo "可以使用: lsof -i:$port 查看占用进程"
            exit 1
        else
            log_success "端口 $port 已释放"
        fi
    fi
}

# 停止现有服务
stop_existing_services() {
    log_info "停止现有服务..."
    
    # 停止后端服务
    local backend_patterns=(
        "uvicorn.*backend.server:app"
        "python.*backend/server.py"
        "uvicorn.*server:app"
    )
    
    for pattern in "${backend_patterns[@]}"; do
        if pgrep -f "$pattern" > /dev/null; then
            log_info "停止后端进程: $pattern"
            pkill -f "$pattern" 2>/dev/null || true
        fi
    done
    
    # 停止前端服务
    local frontend_patterns=(
        "npm.*run.*dev"
        "next.*dev"
        "node.*next.*dev"
        "npm.*run.*start"
        "next.*start"
    )
    
    for pattern in "${frontend_patterns[@]}"; do
        if pgrep -f "$pattern" > /dev/null; then
            log_info "停止前端进程: $pattern"
            pkill -f "$pattern" 2>/dev/null || true
        fi
    done
    
    sleep 2
    log_success "现有服务已停止"
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 检查端口
    check_port $BACKEND_PORT "后端"
    
    # 根据模式选择启动方式
    if [ "$MODE" = "production" ]; then
        log_info "生产模式启动后端..."
        nohup uvicorn backend.server:app \
            --host 0.0.0.0 \
            --port $BACKEND_PORT \
            --workers 4 \
            --access-log \
            > "$BACKEND_LOG" 2>&1 &
    else
        log_info "开发模式启动后端..."
        nohup uvicorn backend.server:app \
            --host 0.0.0.0 \
            --port $BACKEND_PORT \
            --reload \
            > "$BACKEND_LOG" 2>&1 &
    fi
    
    BACKEND_PID=$!
    echo "$BACKEND_PID" > .backend.pid
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:$BACKEND_PORT/health > /dev/null 2>&1; then
            log_success "后端服务启动成功 (PID: $BACKEND_PID)"
            return 0
        fi
        
        if [ $i -eq 30 ]; then
            log_error "后端服务启动超时"
            echo "查看后端日志:"
            tail -20 "$BACKEND_LOG"
            kill $BACKEND_PID 2>/dev/null || true
            exit 1
        fi
        
        sleep 1
    done
}

# 启动前端服务
start_frontend() {
    if [ ! -d "frontend" ]; then
        log_warning "未找到frontend目录，跳过前端启动"
        return 0
    fi
    
    log_info "启动前端服务..."
    
    cd frontend
    
    # 检查端口
    check_port $FRONTEND_PORT "前端"
    
    # 根据模式选择启动方式
    if [ "$MODE" = "production" ]; then
        log_info "生产模式启动前端..."
        # 先构建
        npm run build
        nohup npm run start -- --port $FRONTEND_PORT > "../$FRONTEND_LOG" 2>&1 &
    else
        log_info "开发模式启动前端..."
        nohup npm run dev -- --port $FRONTEND_PORT > "../$FRONTEND_LOG" 2>&1 &
    fi
    
    FRONTEND_PID=$!
    cd ..
    
    echo "$FRONTEND_PID" > .frontend.pid
    
    # 等待前端启动
    log_info "等待前端服务启动..."
    for i in {1..60}; do
        if curl -s http://localhost:$FRONTEND_PORT > /dev/null 2>&1; then
            log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
            return 0
        fi
        
        if [ $i -eq 60 ]; then
            log_warning "前端服务启动可能有问题，请检查 $FRONTEND_LOG"
            echo "查看前端日志:"
            tail -20 "$FRONTEND_LOG"
            break
        fi
        
        sleep 2
    done
}

# 显示启动信息
show_startup_info() {
    echo ""
    echo "🎉 系统启动完成！"
    echo "================================"
    echo "🔧 运行模式: $MODE"
    echo ""
    echo "📊 后端服务:"
    echo "   - API地址: http://localhost:$BACKEND_PORT"
    echo "   - API文档: http://localhost:$BACKEND_PORT/docs"
    echo "   - 健康检查: http://localhost:$BACKEND_PORT/health"
    echo "   - 进程ID: $(cat .backend.pid 2>/dev/null || echo 'N/A')"
    echo "   - 日志文件: $BACKEND_LOG"
    
    if [ -d "frontend" ]; then
        echo ""
        echo "🌐 前端服务:"
        echo "   - 用户界面: http://localhost:$FRONTEND_PORT"
        echo "   - 进程ID: $(cat .frontend.pid 2>/dev/null || echo 'N/A')"
        echo "   - 日志文件: $FRONTEND_LOG"
    fi
    
    echo ""
    echo "🛠️  管理命令:"
    echo "   - 停止服务: ./stop_system.sh"
    echo "   - 重启服务: ./restart_system.sh"
    echo "   - 查看日志: tail -f $BACKEND_LOG"
    echo ""
    echo "💡 提示: 按 Ctrl+C 可以停止日志监控，服务将继续在后台运行"
}

# 监控日志
monitor_logs() {
    if [ "$MODE" = "production" ]; then
        log_info "生产模式，不自动监控日志"
        return 0
    fi

    echo ""
    echo "📊 开始监控后端日志..."
    echo "================================"
    sleep 2

    # 监控后端日志
    tail -f "$BACKEND_LOG" 2>/dev/null || {
        log_warning "无法监控日志文件，可能服务尚未完全启动"
    }
}

# 主函数
main() {
    echo "🚀 AI新闻分析系统启动"
    echo "================================"

    # 解析参数
    parse_arguments "$@"

    log_info "启动模式: $MODE"
    log_info "后端端口: $BACKEND_PORT"
    log_info "前端端口: $FRONTEND_PORT"
    echo ""

    # 执行启动流程
    check_prerequisites
    load_environment
    create_directories
    initialize_databases
    stop_existing_services
    start_backend
    start_frontend
    show_startup_info

    # 监控日志（仅开发模式）
    monitor_logs
}

# 信号处理
cleanup() {
    echo ""
    log_info "接收到停止信号，日志监控已停止"
    log_info "服务仍在后台运行，使用 ./stop_system.sh 停止服务"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
