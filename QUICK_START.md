# AI新闻分析系统 - 快速开始指南

## 🚀 5分钟快速部署

### 前置条件
- Python 3.8+ 
- Node.js 18+
- Git

### 一键部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd cash-flow

# 2. 一键环境部署
./setup_environment.sh

# 3. 启动系统
./start_system.sh
```

### 访问应用
- **用户界面**: http://localhost:3000
- **API文档**: http://localhost:8000/docs

## 📋 部署检查清单

### ✅ 环境检查
- [ ] Python 3.8+ 已安装
- [ ] Node.js 18+ 已安装
- [ ] Git 已安装
- [ ] 网络连接正常

### ✅ API 密钥准备
- [ ] Gemini API Key (必需)
  - 获取: https://makersuite.google.com/app/apikey
- [ ] Tushare Token (可选)
  - 获取: https://tushare.pro/register
- [ ] GLM API Key (可选)
  - 获取: https://open.bigmodel.cn/

### ✅ 部署验证
- [ ] 环境部署脚本运行成功
- [ ] 后端服务启动 (http://localhost:8000/health)
- [ ] 前端服务启动 (http://localhost:3000)
- [ ] API 文档可访问 (http://localhost:8000/docs)

## 🛠️ 常用命令

```bash
# 启动服务
./start_system.sh

# 停止服务
./stop_system.sh

# 重启服务
./restart_system.sh

# 生产模式启动
./start_system.sh --mode production

# 查看日志
tail -f logs/backend.log
tail -f logs/frontend.log
```

## 🔧 快速故障排除

### 端口被占用
```bash
./stop_system.sh
./start_system.sh
```

### 依赖问题
```bash
# 重新安装依赖
./setup_environment.sh
```

### API 密钥问题
```bash
# 重新配置环境变量
./setup_environment.sh
```

## 📖 详细文档

- [完整部署指南](DEPLOYMENT.md)
- [API 文档](http://localhost:8000/docs)
- [系统架构](docs/ARCHITECTURE.md)

## 🆘 需要帮助？

1. 查看 [部署指南](DEPLOYMENT.md) 的故障排除部分
2. 检查日志文件: `logs/backend.log` 和 `logs/frontend.log`
3. 确认系统要求和 API 密钥配置

---

**🎉 恭喜！您的AI新闻分析系统已成功部署！**
