#!/usr/bin/env python3
"""
AI深度分析流式响应同步问题修复测试脚本
验证分析内容的完整性、格式正确性和同步时序的准确性
"""

import requests
import json
import time
import sys
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8000"

def test_stream_sync_analysis():
    """测试流式响应同步修复效果"""
    print("🚀 开始测试AI深度分析流式响应同步修复效果")
    print("=" * 60)
    
    # 测试数据
    test_news = {
        "news_title": "测试新闻：流式响应同步修复验证",
        "news_content": "这是一条用于测试AI深度分析流式响应同步修复效果的测试新闻。主要验证：1) 前端正确等待后端分析完成；2) 分析结果数据传输完整性；3) final_analysis字段正确解析；4) 四层分析结果格式化；5) 流式进度与最终结果同步。",
        "news_source": "测试来源",
        "news_publish_time": datetime.now().isoformat(),
        "analysis_type": "deep",
        "max_research_loops": 1,
        "priority": "medium",
        "use_four_layer_analysis": True
    }
    
    try:
        print("📤 发送深度分析请求...")
        response = requests.post(
            f"{BASE_URL}/news/deep-analysis",
            json=test_news,
            headers={"Content-Type": "application/json"},
            stream=True,
            timeout=180  # 增加超时时间
        )
        
        if response.status_code != 200:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        print("✅ API请求成功，开始接收流式数据...")
        
        # 解析流式响应
        message_count = 0
        task_id = None
        analysis_phases = {
            'four_layer_completed': False,
            'results_saved': False,
            'analysis_completed': False
        }
        final_result = None
        content_length = 0
        
        start_time = time.time()
        last_message_time = start_time
        
        for line in response.iter_lines(decode_unicode=True):
            if not line.strip():
                continue
                
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    message_count += 1
                    current_time = time.time()
                    
                    print(f"📨 [{current_time - start_time:.1f}s] 消息 {message_count}: {data.get('type', 'unknown')}")
                    
                    # 提取任务ID
                    if data.get('task_id') and not task_id:
                        task_id = data['task_id']
                        print(f"🆔 任务ID: {task_id}")
                    
                    # 跟踪分析阶段
                    if data.get('type') == 'four_layer_completed':
                        analysis_phases['four_layer_completed'] = True
                        print("✅ 四层分析完成")
                    elif data.get('type') == 'results_saved':
                        analysis_phases['results_saved'] = True
                        print("✅ 结果保存完成")
                    elif data.get('type') == 'analysis_completed':
                        analysis_phases['analysis_completed'] = True
                        final_result = data.get('result')
                        print("✅ 分析完成消息接收")
                        
                        # 验证分析结果
                        if final_result:
                            print("🔍 验证分析结果...")
                            
                            # 检查final_analysis字段
                            final_analysis = final_result.get('final_analysis')
                            if not final_analysis:
                                print("❌ 缺少final_analysis字段")
                                return False
                            
                            # 计算内容长度
                            if isinstance(final_analysis, str):
                                content_length = len(final_analysis)
                            elif isinstance(final_analysis, dict):
                                if final_analysis.get('analysis_type') == 'four_layer_thinking_chain':
                                    # 四层分析格式
                                    layers = [
                                        final_analysis.get('layer1_analysis', ''),
                                        final_analysis.get('layer2_analysis', ''),
                                        final_analysis.get('layer3_analysis', ''),
                                        final_analysis.get('layer4_analysis', ''),
                                        final_analysis.get('analysis', '')
                                    ]
                                    content_length = sum(len(layer) for layer in layers if layer)
                                    
                                    print(f"📊 四层分析内容统计:")
                                    for i, layer in enumerate(layers[:4], 1):
                                        if layer:
                                            print(f"   第{i}层: {len(layer)}字符")
                                    if layers[4]:
                                        print(f"   总结: {len(layers[4])}字符")
                                    
                                    # 检查投资目标
                                    targets = final_analysis.get('investment_targets', [])
                                    if targets:
                                        print(f"   投资目标: {len(targets)}个")
                                    
                                    # 检查置信度
                                    confidence = final_analysis.get('confidence')
                                    if confidence:
                                        print(f"   置信度: {confidence}")
                                        
                                elif final_analysis.get('analysis'):
                                    content_length = len(final_analysis['analysis'])
                            
                            print(f"📏 分析内容总长度: {content_length}字符")
                            
                            # 验证内容完整性
                            if content_length < 500:
                                print(f"⚠️ 分析内容可能过短 ({content_length}字符)")
                            else:
                                print(f"✅ 分析内容长度合理 ({content_length}字符)")
                            
                            # 检查其他字段
                            if final_result.get('task_id'):
                                print(f"✅ 包含任务ID: {final_result['task_id']}")
                            if final_result.get('completed_at'):
                                print(f"✅ 包含完成时间: {final_result['completed_at']}")
                            if final_result.get('sources'):
                                print(f"✅ 包含来源信息: {len(final_result['sources'])}个")
                        
                        break
                        
                    # 检查错误
                    if data.get('type') == 'error':
                        print(f"❌ 分析过程中出现错误: {data.get('message', 'Unknown error')}")
                        return False
                    
                    last_message_time = current_time
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON解析失败: {e}")
                    print(f"原始数据: {line}")
                    continue
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("📊 测试结果统计")
        print("=" * 60)
        
        print(f"总耗时: {total_duration:.1f}秒")
        print(f"接收消息数: {message_count}")
        print(f"任务ID: {task_id or '未获取到'}")
        print(f"分析内容长度: {content_length}字符")
        
        print("\n分析阶段完成情况:")
        for phase, completed in analysis_phases.items():
            status = "✅ 完成" if completed else "❌ 未完成"
            print(f"  {phase}: {status}")
        
        # 验证同步性
        all_phases_complete = all(analysis_phases.values())
        has_valid_content = content_length >= 500
        
        print(f"\n同步验证:")
        print(f"  所有阶段完成: {'✅' if all_phases_complete else '❌'}")
        print(f"  内容完整性: {'✅' if has_valid_content else '❌'}")
        print(f"  最终结果: {'✅' if final_result else '❌'}")
        
        # 总体评估
        success = all_phases_complete and has_valid_content and final_result
        
        print(f"\n🎯 总体评估: {'✅ 修复成功' if success else '❌ 仍有问题'}")
        
        if success:
            print("\n🎉 流式响应同步问题修复验证通过！")
            print("主要改进:")
            print("  ✅ 前端正确等待后端分析完成")
            print("  ✅ 分析结果数据传输完整")
            print("  ✅ final_analysis字段正确解析")
            print("  ✅ 流式进度与最终结果同步")
        else:
            print("\n⚠️ 修复验证未完全通过，需要进一步检查:")
            if not all_phases_complete:
                print("  - 分析阶段未全部完成")
            if not has_valid_content:
                print("  - 分析内容长度不足")
            if not final_result:
                print("  - 缺少最终结果")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 AI深度分析流式响应同步修复验证")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查后端服务
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return 1
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return 1
    
    # 执行测试
    success = test_stream_sync_analysis()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
