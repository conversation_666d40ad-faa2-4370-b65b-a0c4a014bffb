# AI新闻分析系统 (Cash Flow)

AI新闻分析系统是一个基于人工智能的综合性新闻分析平台，专注于AI新闻的智能分析和深度解读。系统采用模块化架构设计，包含强大的后端数据处理和AI计算能力，以及动态的前端交互体验。

## ✨ 核心功能

- 🤖 **AI新闻分析**: 智能解读AI相关新闻，提供深度分析
- 🔍 **四层漏斗分析**: 从事件感知到具体标的的精准分析框架
- 🧠 **多模型支持**: 集成Gemini、GLM等多种AI模型
- 📊 **实时数据**: 支持实时新闻获取和分析
- 💬 **智能对话**: AI投资助手聊天界面

## 🚀 快速开始

### 一键部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd cash-flow

# 2. 一键环境部署
./setup_environment.sh

# 3. 启动系统
./start_system.sh
```

### 访问应用
- **用户界面**: http://localhost:3000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 📋 系统要求

- **Python**: 3.8+ (推荐 3.11+)
- **Node.js**: 18.0+ (推荐 20.0+)
- **内存**: 4GB RAM (推荐 8GB+)
- **存储**: 2GB 可用空间

## 🔑 API 密钥配置

系统需要以下API密钥：

**必需**:
- `Gemini API Key`: 用于AI分析功能
  - 获取地址: https://makersuite.google.com/app/apikey

**可选**:
- `Tushare Token`: 用于股票数据
  - 获取地址: https://tushare.pro/register
- `GLM API Key`: 用于智谱AI
  - 获取地址: https://open.bigmodel.cn/

## 🏗️ 系统架构

### 后端 (Backend)

后端是系统的核心，负责所有数据处理、AI计算和API服务。基于Python构建，集成多种AI模型和数据源。

**核心组件**:
- 🤖 **AI模块** (`backend/ai/`): AI新闻分析、深度分析、模型集成
- 🔌 **API层** (`backend/apis/`): RESTful API接口
- 🧠 **核心业务** (`backend/core/`): 数据管理、分析引擎
- 🔧 **服务层** (`backend/services/`): 新闻服务、市场数据、任务调度
- 🔐 **认证模块** (`backend/auth/`): 用户管理和认证
- 🛠️ **工具库** (`backend/utils/`): 通用工具函数

### 前端 (Frontend)

前端提供用户友好的交互界面，基于Next.js和React构建，支持实时数据展示和AI对话。

**核心组件**:
- 🎨 **用户界面**: 现代化的响应式设计
- 📊 **数据可视化**: 图表和分析结果展示
- 💬 **AI聊天**: 智能对话界面
- 🔄 **实时更新**: WebSocket实时数据推送

## 🛠️ 服务管理

### 启动服务
```bash
# 开发模式启动
./start_system.sh

# 生产模式启动
./start_system.sh --mode production

# 自定义端口启动
./start_system.sh --backend-port 8080 --frontend-port 3001
```

### 停止服务
```bash
./stop_system.sh
```

### 重启服务
```bash
./restart_system.sh
```

### 查看日志
```bash
# 查看后端日志
tail -f logs/backend.log

# 查看前端日志
tail -f logs/frontend.log
```

### 服务状态检查
```bash
# 检查服务进程
ps aux | grep -E 'uvicorn|next'

# 检查端口占用
lsof -i:8000 -i:3000

# 健康检查
curl http://localhost:8000/health
```

## 📚 文档

- 📖 [完整部署指南](DEPLOYMENT.md) - 详细的部署说明和配置
- 🚀 [快速开始指南](QUICK_START.md) - 5分钟快速部署
- 🏗️ [系统架构](docs/ARCHITECTURE.md) - 系统设计和架构说明
- 🔧 [API文档](http://localhost:8000/docs) - 完整的API接口文档

## 🔧 故障排除

### 常见问题

**端口被占用**
```bash
./stop_system.sh
./start_system.sh
```

**依赖问题**
```bash
./setup_environment.sh  # 重新安装依赖
```

**API密钥问题**
```bash
./setup_environment.sh  # 重新配置环境变量
```

更多故障排除信息请查看 [部署指南](DEPLOYMENT.md)。

## 🤝 贡献

欢迎贡献代码和提出建议！请查看 [贡献指南](CONTRIBUTING) 了解详细信息。

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。
