#!/usr/bin/env python3
"""
数据库初始化测试脚本
验证数据库自动初始化功能是否正常工作
"""

import os
import sqlite3
import tempfile
import shutil
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from backend.database_initializer import DatabaseInitializer

def test_database_initialization():
    """测试数据库初始化功能"""
    print("🧪 开始测试数据库初始化功能")
    print("="*60)
    
    # 创建临时测试目录
    test_dir = tempfile.mkdtemp(prefix="db_test_")
    print(f"📁 测试目录: {test_dir}")
    
    try:
        # 初始化数据库
        initializer = DatabaseInitializer(data_dir=test_dir)
        success = initializer.initialize_all()
        
        if not success:
            print("❌ 数据库初始化失败")
            return False
        
        # 验证数据库文件
        print("\n🔍 验证数据库文件...")
        expected_databases = [
            "financial_data.db",
            "financial_news.db", 
            "users.db",
            "news_impact_analysis.db",
            "divergence_data.db"
        ]
        
        for db_file in expected_databases:
            db_path = Path(test_dir) / db_file
            if db_path.exists():
                print(f"✅ {db_file} 存在")
                
                # 测试数据库连接
                try:
                    with sqlite3.connect(db_path) as conn:
                        conn.execute("SELECT 1")
                    print(f"✅ {db_file} 连接正常")
                except Exception as e:
                    print(f"❌ {db_file} 连接失败: {e}")
                    return False
            else:
                print(f"❌ {db_file} 不存在")
                return False
        
        # 验证表结构
        print("\n🔍 验证表结构...")
        table_tests = [
            ("financial_data.db", ["stock_info", "cn_daily_data", "us_daily_data", "factor_data", "user_watchlist"]),
            ("financial_news.db", ["financial_news", "news_impact_analysis"]),
            ("users.db", ["users", "user_sessions"]),
            ("news_impact_analysis.db", ["news_deep_research", "deep_research_queue"]),
            ("divergence_data.db", ["divergence_signals"])
        ]
        
        for db_file, expected_tables in table_tests:
            db_path = Path(test_dir) / db_file
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = [row[0] for row in cursor.fetchall()]
                
                for table in expected_tables:
                    if table in existing_tables:
                        print(f"✅ {db_file}:{table} 表存在")
                    else:
                        print(f"❌ {db_file}:{table} 表不存在")
                        return False
        
        # 测试基本数据操作
        print("\n🔍 测试基本数据操作...")
        
        # 测试用户数据库
        users_db = Path(test_dir) / "users.db"
        with sqlite3.connect(users_db) as conn:
            # 插入测试用户
            conn.execute("""
                INSERT INTO users (username, email, password_hash, full_name)
                VALUES (?, ?, ?, ?)
            """, ("testuser", "<EMAIL>", "hash123", "Test User"))
            
            # 查询用户
            cursor = conn.execute("SELECT username, email FROM users WHERE username = ?", ("testuser",))
            result = cursor.fetchone()
            
            if result and result[0] == "testuser":
                print("✅ 用户数据库操作正常")
            else:
                print("❌ 用户数据库操作失败")
                return False
        
        # 测试新闻数据库
        news_db = Path(test_dir) / "financial_news.db"
        with sqlite3.connect(news_db) as conn:
            # 插入测试新闻
            conn.execute("""
                INSERT INTO financial_news (news_id, title, content, publish_time, source, source_name)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ("test001", "测试新闻", "测试内容", "2024-01-01 10:00:00", "test", "测试源"))
            
            # 查询新闻
            cursor = conn.execute("SELECT title FROM financial_news WHERE news_id = ?", ("test001",))
            result = cursor.fetchone()
            
            if result and result[0] == "测试新闻":
                print("✅ 新闻数据库操作正常")
            else:
                print("❌ 新闻数据库操作失败")
                return False
        
        print("\n🎉 所有测试通过！数据库初始化功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            print(f"🧹 清理测试目录: {test_dir}")
        except Exception as e:
            print(f"⚠️  清理测试目录失败: {e}")

def test_in_clean_environment():
    """在干净环境中测试（删除现有数据库文件）"""
    print("\n🧪 测试在干净环境中的数据库初始化")
    print("="*60)
    
    # 备份现有数据库文件
    data_dir = Path("data")
    backup_dir = Path("data_backup_test")
    
    if data_dir.exists():
        if backup_dir.exists():
            shutil.rmtree(backup_dir)
        shutil.copytree(data_dir, backup_dir)
        print(f"📦 备份现有数据库到: {backup_dir}")
        
        # 删除现有数据库文件
        for db_file in data_dir.glob("*.db"):
            db_file.unlink()
            print(f"🗑️  删除: {db_file}")
    
    try:
        # 运行数据库初始化
        initializer = DatabaseInitializer()
        success = initializer.initialize_all()
        
        if success:
            print("✅ 在干净环境中数据库初始化成功")
        else:
            print("❌ 在干净环境中数据库初始化失败")
            
        return success
        
    finally:
        # 恢复备份
        if backup_dir.exists():
            if data_dir.exists():
                shutil.rmtree(data_dir)
            shutil.move(backup_dir, data_dir)
            print(f"🔄 恢复数据库备份")

def main():
    """主函数"""
    print("🚀 数据库初始化功能测试")
    print("="*60)
    
    # 测试1: 在临时目录中测试
    test1_success = test_database_initialization()
    
    # 测试2: 在干净环境中测试
    print("\n" + "="*60)
    test2_success = test_in_clean_environment()
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    print(f"临时目录测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"干净环境测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！数据库自动初始化功能正常工作")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查数据库初始化逻辑")
        return 1

if __name__ == "__main__":
    exit(main())
