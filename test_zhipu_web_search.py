import os
from zhipuai import ZhipuAI

def test_zhipu_web_search():
    """
    Tests if the ZhipuAI glm-4-flash model can perform web searches.
    """
    # --- 请在此处填写您的 ZHIPUAI API KEY ---
    # 为了安全，强烈建议您使用环境变量来设置 API Key。
    # 您可以在这里找到您的 API Key: https://open.bigmodel.cn/usercenter/apikeys
    api_key = ""  # <--- 在这里填入您的 KEY

    # 尝试从环境变量中读取 API Key
    if not api_key:
        api_key = os.environ.get("GLM_API_KEY")

    if not api_key:
        print("错误：ZhipuAI API Key 未设置。")
        print("请在脚本中直接填写您的 API Key，或者设置名为 ZHIPUAI_API_KEY 的环境变量。")
        return

    try:
        client = ZhipuAI(api_key=api_key)

        # 1. 准备一个需要网络搜索才能回答的问题
        user_question = "最近的A股市场有哪些重要新闻？"
        print("--- 测试智谱AI glm-4-flash 网络搜索功能 ---")
        print(f"模型: glm-4-flash")
        print(f"问题: {user_question}")
        print("-" * 50)

        # 2. 构建消息和工具
        # 根据智谱官方文档，模型会智能地根据用户问题决定是否以及如何进行搜索
        messages = [
            {"role": "system", "content": "你是一个能够利用网络搜索的智能助手，请根据实时信息回答问题。"},
            {"role": "user", "content": user_question}
        ]
        
        tools = [
            {
                "type": "web_search",
                "web_search": {
                    "enable": True,
                    # 根据智谱官方文档，如果此处不指定 search_query，模型会根据上下文自动生成搜索查询。
                }
            }
        ]

        # 3. 发起请求
        print("正在向智谱AI发起请求...")
        response = client.chat.completions.create(
            model="glm-4-flash",
            messages=messages,
            tools=tools,
            stream=False  # 设置为 False 以便查看完整的返回对象
        )

        # 4. 解析并打印结果
        print("\n--- API 返回的完整响应 ---")
        print(response)
        print("-" * 50)

        if response.choices:
            choice = response.choices[0]
            print("\n--- 模型的回复 ---")
            print(choice.message.content)
            print("-" * 50)

            # 检查模型是否调用了网络搜索工具
            if choice.message.tool_calls and any(tc.type == 'web_search' for tc in choice.message.tool_calls):
                print("\n✅ 测试成功: 模型已成功调用网络搜索工具。")
                print("工具调用详情:")
                print(choice.message.tool_calls)
            else:
                print("\n⚠️ 测试结果不确定: 模型回复中未发现明确的工具调用。")
                print("这可能是因为模型直接整合了搜索结果，或者选择不使用搜索。")
                print("请检查上面的回复内容是否包含最新的网络信息。")

        else:
            print("\n❌ 测试失败: API响应中没有返回任何内容。")

    except Exception as e:
        print(f"\n--- 测试过程中发生错误 ---")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误详情: {e}")
        print("请检查您的 API Key 是否正确、账户余额是否充足以及网络连接。")

if __name__ == "__main__":
    test_zhipu_web_search() 