#!/bin/bash
# AI新闻分析系统 - 部署验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🔍 AI新闻分析系统 - 部署验证"
echo "================================"

# 验证计数器
total_checks=0
passed_checks=0

# 验证函数
verify_check() {
    local description=$1
    local command=$2
    local expected_result=${3:-0}
    
    total_checks=$((total_checks + 1))
    log_info "检查: $description"
    
    if eval "$command" >/dev/null 2>&1; then
        if [ $? -eq $expected_result ]; then
            log_success "$description - 通过"
            passed_checks=$((passed_checks + 1))
            return 0
        fi
    fi
    
    log_error "$description - 失败"
    return 1
}

# 1. 检查文件结构
log_info "检查项目文件结构..."

verify_check "项目根目录存在pyproject.toml" "[ -f 'pyproject.toml' ]"
verify_check "后端目录存在" "[ -d 'backend' ]"
verify_check "前端目录存在" "[ -d 'frontend' ]"
verify_check "环境变量文件存在" "[ -f '.env' ]"
verify_check "Python虚拟环境存在" "[ -d '.venv' ]"

# 2. 检查脚本文件
log_info "检查部署脚本..."

verify_check "环境部署脚本存在且可执行" "[ -x 'setup_environment.sh' ]"
verify_check "启动脚本存在且可执行" "[ -x 'start_system.sh' ]"
verify_check "停止脚本存在且可执行" "[ -x 'stop_system.sh' ]"
verify_check "重启脚本存在且可执行" "[ -x 'restart_system.sh' ]"

# 3. 检查Python环境
log_info "检查Python环境..."

verify_check "Python3可用" "command -v python3"
verify_check "虚拟环境可激活" "source .venv/bin/activate"

if source .venv/bin/activate 2>/dev/null; then
    verify_check "FastAPI已安装" "python -c 'import fastapi'"
    verify_check "Uvicorn已安装" "python -c 'import uvicorn'"
    verify_check "后端服务器文件存在" "[ -f 'backend/server.py' ]"
fi

# 4. 检查Node.js环境
log_info "检查Node.js环境..."

verify_check "Node.js可用" "command -v node"
verify_check "npm可用" "command -v npm"

if [ -d "frontend" ]; then
    verify_check "前端依赖已安装" "[ -d 'frontend/node_modules' ]"
    verify_check "Next.js配置存在" "[ -f 'frontend/next.config.ts' ]"
    verify_check "前端package.json存在" "[ -f 'frontend/package.json' ]"
fi

# 5. 检查环境变量
log_info "检查环境变量配置..."

if [ -f ".env" ]; then
    verify_check "Gemini API Key已配置" "grep -q 'GEMINI_API_KEY=' .env"
    verify_check "服务器端口已配置" "grep -q 'SERVER_PORT=' .env"
else
    log_error "环境变量文件不存在"
fi

# 6. 检查必要目录
log_info "检查必要目录..."

verify_check "数据目录存在" "[ -d 'data' ]"
verify_check "日志目录存在" "[ -d 'logs' ]"

# 7. 检查数据库文件
log_info "检查数据库文件..."

databases=(
    "data/financial_data.db"
    "data/financial_news.db"
    "data/users.db"
    "data/news_impact_analysis.db"
    "data/divergence_data.db"
)

for db_file in "${databases[@]}"; do
    db_name=$(basename "$db_file" .db)
    verify_check "${db_name}数据库存在" "[ -f '$db_file' ]"
done

# 8. 检查数据库初始化器
verify_check "数据库初始化器存在" "[ -f 'backend/database_initializer.py' ]"

# 9. 检查服务状态（如果正在运行）
log_info "检查服务状态..."

if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    verify_check "后端服务响应正常" "curl -s http://localhost:8000/health"
    log_success "后端服务正在运行"
else
    log_warning "后端服务未运行（这是正常的，如果您还未启动服务）"
fi

if curl -s http://localhost:3000 >/dev/null 2>&1; then
    verify_check "前端服务响应正常" "curl -s http://localhost:3000"
    log_success "前端服务正在运行"
else
    log_warning "前端服务未运行（这是正常的，如果您还未启动服务）"
fi

# 10. 检查文档
log_info "检查文档文件..."

verify_check "部署指南存在" "[ -f 'DEPLOYMENT.md' ]"
verify_check "快速开始指南存在" "[ -f 'QUICK_START.md' ]"
verify_check "README文件存在" "[ -f 'README.md' ]"

# 显示验证结果
echo ""
echo "📊 验证结果"
echo "================================"
echo "总检查项: $total_checks"
echo "通过检查: $passed_checks"
echo "失败检查: $((total_checks - passed_checks))"

if [ $passed_checks -eq $total_checks ]; then
    log_success "所有检查通过！部署验证成功 🎉"
    echo ""
    echo "🚀 接下来的步骤："
    echo "  1. 启动服务: ./start_system.sh"
    echo "  2. 访问应用: http://localhost:3000"
    echo "  3. 查看API: http://localhost:8000/docs"
    exit 0
elif [ $passed_checks -gt $((total_checks * 3 / 4)) ]; then
    log_warning "大部分检查通过，但有一些问题需要解决"
    echo ""
    echo "🔧 建议操作："
    echo "  1. 检查失败的项目"
    echo "  2. 重新运行: ./setup_environment.sh"
    echo "  3. 查看详细文档: DEPLOYMENT.md"
    exit 1
else
    log_error "多项检查失败，需要重新部署"
    echo ""
    echo "🆘 建议操作："
    echo "  1. 重新运行环境部署: ./setup_environment.sh"
    echo "  2. 检查系统要求是否满足"
    echo "  3. 查看部署指南: DEPLOYMENT.md"
    exit 2
fi
