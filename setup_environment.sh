#!/bin/bash
# 智能环境部署脚本 - AI新闻分析系统
# 自动检测系统环境并安装所有必要依赖

set -e  # 遇到错误时退出

echo "🚀 AI新闻分析系统 - 智能环境部署"
echo "================================"
echo "此脚本将自动配置完整的运行环境"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查系统要求
check_system_requirements() {
    log_info "检查系统要求..."

    # 检查操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macOS"
        log_success "检测到 macOS 系统"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="Linux"
        log_success "检测到 Linux 系统"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi

    # 检查必要工具
    local required_tools=("curl" "git")
    for tool in "${required_tools[@]}"; do
        if command_exists "$tool"; then
            log_success "$tool 已安装"
        else
            log_error "$tool 未安装，请先安装此工具"
            exit 1
        fi
    done
}

# 检查并安装Python
check_install_python() {
    log_info "检查Python环境..."

    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
            log_success "Python $PYTHON_VERSION 已安装且版本符合要求"
        else
            log_error "Python版本过低 ($PYTHON_VERSION)，需要Python 3.8+"
            exit 1
        fi
    else
        log_error "Python3 未安装"
        if [[ "$OS" == "macOS" ]]; then
            log_info "请安装Python: brew install python@3.11"
        else
            log_info "请安装Python: sudo apt-get install python3 python3-pip"
        fi
        exit 1
    fi
}

# 检查并安装Node.js
check_install_nodejs() {
    log_info "检查Node.js环境..."

    if command_exists node; then
        NODE_VERSION=$(node --version | sed 's/v//')
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1)

        if [ "$NODE_MAJOR" -ge 18 ]; then
            log_success "Node.js $NODE_VERSION 已安装且版本符合要求"
        else
            log_warning "Node.js版本较低 ($NODE_VERSION)，建议升级到18+"
        fi
    else
        log_error "Node.js 未安装"
        if [[ "$OS" == "macOS" ]]; then
            log_info "请安装Node.js: brew install node"
        else
            log_info "请安装Node.js: https://nodejs.org/"
        fi
        exit 1
    fi

    # 检查npm
    if command_exists npm; then
        log_success "npm $(npm --version) 已安装"
    else
        log_error "npm 未安装"
        exit 1
    fi
}

# 检查并安装uv包管理器
check_install_uv() {
    log_info "检查uv包管理器..."

    if command_exists uv; then
        UV_VERSION=$(uv --version | cut -d' ' -f2)
        log_success "uv $UV_VERSION 已安装"
    else
        log_info "安装uv包管理器..."
        if [[ "$OS" == "macOS" ]]; then
            if command_exists brew; then
                brew install uv
            else
                curl -LsSf https://astral.sh/uv/install.sh | sh
            fi
        else
            curl -LsSf https://astral.sh/uv/install.sh | sh
        fi

        # 重新加载PATH
        export PATH="$HOME/.cargo/bin:$PATH"

        if command_exists uv; then
            log_success "uv 安装成功"
        else
            log_error "uv 安装失败"
            exit 1
        fi
    fi
}

# 设置Python虚拟环境
setup_python_env() {
    log_info "设置Python虚拟环境..."

    # 使用uv创建虚拟环境
    if [ ! -d ".venv" ]; then
        log_info "创建Python虚拟环境..."
        uv venv
        log_success "虚拟环境创建完成"
    else
        log_success "虚拟环境已存在"
    fi

    # 激活虚拟环境
    source .venv/bin/activate
    log_success "虚拟环境已激活"

    # 安装Python依赖
    log_info "安装Python依赖..."
    if [ -f "pyproject.toml" ]; then
        uv pip install -e .
        log_success "从pyproject.toml安装依赖完成"
    elif [ -f "requirements.txt" ]; then
        uv pip install -r requirements.txt
        log_success "从requirements.txt安装依赖完成"
    else
        log_warning "未找到依赖配置文件"
    fi

    # 安装后端特定依赖
    if [ -f "backend/requirements.txt" ]; then
        log_info "安装后端依赖..."
        uv pip install -r backend/requirements.txt
        log_success "后端依赖安装完成"
    fi
}

# 设置前端环境
setup_frontend_env() {
    if [ -d "frontend" ]; then
        log_info "设置前端环境..."
        cd frontend

        # 安装前端依赖
        log_info "安装前端依赖..."
        npm ci
        log_success "前端依赖安装完成"

        cd ..
    else
        log_warning "未找到frontend目录，跳过前端环境设置"
    fi
}

# 配置环境变量
setup_environment_variables() {
    log_info "配置环境变量..."

    # 检查是否已有.env文件
    if [ -f ".env" ]; then
        log_warning "发现现有.env文件"
        echo -n "是否要重新配置环境变量？(y/N): "
        read -r reconfigure_choice
        if [[ ! $reconfigure_choice =~ ^[Yy]$ ]]; then
            log_info "跳过环境变量配置"
            return
        fi

        # 备份现有文件
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        log_success "已备份现有.env文件"
    fi

    echo ""
    echo "请输入您的API密钥和Token："

    # 获取Gemini API Key
    echo -n "🤖 Gemini API Key (用于AI分析功能): "
    read -r gemini_key
    while [ -z "$gemini_key" ]; do
        echo -n "❗ Gemini API Key不能为空，请重新输入: "
        read -r gemini_key
    done

    # 获取Tushare Token (可选)
    echo -n "📊 Tushare Token (用于股票数据，可选): "
    read -r tushare_token

    # 获取GLM API Key (可选)
    echo -n "🧠 GLM API Key (用于智谱AI，可选): "
    read -r glm_key

    # 获取可选配置
    echo -n "🌐 Gemini Base URL (可选，留空使用默认): "
    read -r gemini_base_url
    if [ -z "$gemini_base_url" ]; then
        gemini_base_url="https://generativelanguage.googleapis.com/v1beta"
    fi

    # 创建.env文件
    cat > .env << EOF
# AI模型配置
GEMINI_API_KEY=$gemini_key
GEMINI_BASE_URL=$gemini_base_url
GEMINI_MODEL=gemini-2.5-flash-lite-preview-06-17

# 智谱AI配置
GLM_API_KEY=$glm_key
GLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

# 数据源配置
TUSHARE_TOKEN=$tushare_token

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
FRONTEND_PORT=3000

# 环境配置
NODE_ENV=development
LOG_LEVEL=INFO

# 数据库配置
DATABASE_PATH=./data
EOF

    log_success "环境变量配置完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    local dirs=("data" "logs" "temp")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
        fi
    done
}

# 初始化数据库
initialize_databases() {
    log_info "初始化数据库..."

    # 确保虚拟环境已激活
    if [ -d ".venv" ]; then
        source .venv/bin/activate

        # 检查数据库初始化器是否存在
        if [ -f "backend/database_initializer.py" ]; then
            log_info "执行数据库初始化..."

            # 运行数据库初始化器
            if python backend/database_initializer.py; then
                log_success "数据库初始化完成"
            else
                log_warning "数据库初始化失败，请检查错误信息"
            fi
        else
            log_warning "未找到数据库初始化器"
        fi
    else
        log_warning "虚拟环境未找到，跳过数据库初始化"
    fi
}

# 验证安装
verify_installation() {
    log_info "验证安装..."

    local success=true

    # 检查Python环境
    if source .venv/bin/activate && python -c "import fastapi, uvicorn" 2>/dev/null; then
        log_success "Python后端环境验证通过"
    else
        log_error "Python后端环境验证失败"
        success=false
    fi

    # 检查前端环境
    if [ -d "frontend" ]; then
        if cd frontend && npm list next >/dev/null 2>&1; then
            log_success "前端环境验证通过"
            cd ..
        else
            log_error "前端环境验证失败"
            success=false
        fi
    fi

    # 检查环境变量
    if [ -f ".env" ]; then
        log_success "环境变量文件存在"
    else
        log_error "环境变量文件不存在"
        success=false
    fi

    return $success
}

# 显示完成信息
show_completion_info() {
    echo ""
    echo "🎉 环境部署完成！"
    echo "================================"
    echo ""
    echo "📋 部署摘要："
    echo "  ✅ Python环境: $(python3 --version)"
    echo "  ✅ Node.js环境: $(node --version)"
    echo "  ✅ uv包管理器: $(uv --version | cut -d' ' -f2)"
    echo "  ✅ 虚拟环境: .venv"
    echo "  ✅ 环境变量: .env"
    echo ""
    echo "🚀 接下来的步骤："
    echo "  1. 启动服务: ./start_system.sh"
    echo "  2. 访问应用: http://localhost:3000"
    echo "  3. API文档: http://localhost:8000/docs"
    echo ""
    echo "📚 更多信息："
    echo "  - 查看日志: tail -f logs/*.log"
    echo "  - 停止服务: ./stop_system.sh"
    echo "  - 重启服务: ./restart_system.sh"
    echo ""
    echo "🔒 安全提醒："
    echo "  请妥善保管.env文件中的API密钥"
    echo "  不要将.env文件提交到版本控制系统"
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -f "pyproject.toml" ] && [ ! -f "package.json" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi

    echo "开始环境部署流程..."
    echo ""

    # 执行部署步骤
    check_system_requirements
    check_install_python
    check_install_nodejs
    check_install_uv
    setup_python_env
    setup_frontend_env
    setup_environment_variables
    create_directories
    initialize_databases

    # 验证安装
    if verify_installation; then
        show_completion_info
    else
        log_error "环境部署验证失败，请检查错误信息"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi