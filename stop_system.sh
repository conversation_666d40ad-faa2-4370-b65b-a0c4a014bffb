#!/bin/bash
# AI新闻分析系统 - 停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🛑 停止AI新闻分析系统"
echo "================================"

# 通过PID文件停止服务
stop_process_by_pid() {
    local service_name=$1
    local pid_file=$2
    shift 2
    local patterns=("$@")
    
    log_info "停止${service_name}服务..."
    
    # 尝试通过PID文件停止
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "通过PID文件停止进程 $pid"
            kill -TERM "$pid" 2>/dev/null || true
            sleep 3
            
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "进程 $pid 未响应TERM信号，使用KILL信号"
                kill -KILL "$pid" 2>/dev/null || true
                sleep 1
            fi
            
            if ! kill -0 "$pid" 2>/dev/null; then
                log_success "${service_name}服务已停止"
                rm -f "$pid_file"
                return 0
            fi
        else
            log_warning "PID文件中的进程 $pid 不存在，清理PID文件"
            rm -f "$pid_file"
        fi
    fi
    
    # 通过进程模式匹配停止
    local stopped=false
    for pattern in "${patterns[@]}"; do
        if pgrep -f "$pattern" > /dev/null; then
            log_info "通过模式匹配停止进程: $pattern"
            pkill -TERM -f "$pattern" 2>/dev/null || true
            sleep 2
            
            if pgrep -f "$pattern" > /dev/null; then
                log_warning "进程未响应TERM信号，使用KILL信号"
                pkill -KILL -f "$pattern" 2>/dev/null || true
                sleep 1
            fi
            
            if ! pgrep -f "$pattern" > /dev/null; then
                stopped=true
            fi
        fi
    done
    
    if [ "$stopped" = true ]; then
        log_success "${service_name}服务已停止"
    else
        log_info "${service_name}服务未运行或已停止"
    fi
}

# 停止后端服务
backend_patterns=(
    "uvicorn.*backend.server:app"
    "python.*backend/server.py"
    "uvicorn.*server:app"
)
stop_process_by_pid "后端" ".backend.pid" "${backend_patterns[@]}"

# 停止前端服务
frontend_patterns=(
    "npm.*run.*dev"
    "next.*dev"
    "node.*next.*dev"
    "npm.*run.*start"
    "next.*start"
)
stop_process_by_pid "前端" ".frontend.pid" "${frontend_patterns[@]}"

# 最终检查并清理所有可能残留的进程
log_info "最终检查并清理残留进程..."
all_patterns=(
    "${backend_patterns[@]}"
    "${frontend_patterns[@]}"
)

all_clean=true
for pattern in "${all_patterns[@]}"; do
    if pgrep -f "$pattern" > /dev/null; then
        log_warning "发现残留进程: $pattern - 强制停止..."
        pkill -KILL -f "$pattern" 2>/dev/null || true
        sleep 1
        if pgrep -f "$pattern" > /dev/null; then
            log_error "进程 $pattern 仍然存在，请手动检查"
            all_clean=false
        else
            log_success "进程 $pattern 已清除"
        fi
    fi
done

# 检查端口占用
log_info "检查端口占用情况..."
ports=(8000 3000)
for port in "${ports[@]}"; do
    if lsof -ti:$port > /dev/null 2>&1; then
        log_warning "端口 $port 仍被占用，尝试释放..."
        lsof -ti:$port | xargs kill -KILL 2>/dev/null || true
        sleep 1
        if ! lsof -ti:$port > /dev/null 2>&1; then
            log_success "端口 $port 已释放"
        else
            log_error "端口 $port 仍被占用，请手动检查"
            all_clean=false
        fi
    fi
done

# 清理PID文件
log_info "清理PID文件..."
for pid_file in .backend.pid .frontend.pid; do
    if [ -f "$pid_file" ]; then
        rm -f "$pid_file"
        log_success "已删除 $pid_file"
    fi
done

# 显示结果
echo ""
if [ "$all_clean" = true ]; then
    log_success "系统已完全停止"
else
    log_warning "部分进程或端口可能未能完全停止，请手动检查"
    echo ""
    echo "🔍 手动检查命令："
    echo "  - 检查后端进程: ps aux | grep -E 'uvicorn|backend'"
    echo "  - 检查前端进程: ps aux | grep -E 'npm|next|node'"
    echo "  - 检查端口占用: lsof -i:8000 -i:3000"
fi

echo ""
echo "📝 日志文件保留："
if [ -f "logs/backend.log" ]; then
    echo "  - logs/backend.log (后端日志)"
fi
if [ -f "logs/frontend.log" ]; then
    echo "  - logs/frontend.log (前端日志)"
fi

echo ""
echo "🚀 重新启动: ./start_system.sh"
