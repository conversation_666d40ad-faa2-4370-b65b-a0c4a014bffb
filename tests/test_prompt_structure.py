#!/usr/bin/env python3
"""
测试重构后的提示词结构和第一性原理内容

验证提示词是否包含第一性原理分析框架
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.ai.llm import LLMManager
from backend.ai.llms.glm_client import GLMClient


def test_gemini_prompt_structure():
    """测试Gemini提示词结构"""
    print("🧪 测试Gemini提示词结构...")
    
    llm_manager = LLMManager()
    
    # 模拟构建提示词的过程
    test_title = "测试新闻标题"
    test_content = "测试新闻内容"
    
    # 从analyze_news_impact_with_gemini方法中提取提示词构建逻辑
    prompt_template = """
你是一位专业的金融分析师，请运用第一性原理方法分析以下新闻对金融市场的潜在影响。

【分析方法论：第一性原理框架】
第一性原理要求从最基本的经济规律出发，通过逻辑推导而非经验类比进行分析。

基础经济假设：
1. 市场参与者在信息约束下追求理性决策
2. 价格机制反映供需关系变化
3. 资本追求风险调整后的最大收益
4. 信息传播存在时滞和不对称性

分析框架：
第一步：事件本质分析 - 识别事件改变了哪些基础经济变量（供给、需求、成本、技术、政策等）
第二步：经济机制识别 - 确定影响传导的核心经济机制（价格机制、竞争机制、替代机制等）
第三步：传导路径分析 - 追踪影响在经济系统中的传播路径和时间序列
第四步：量化评估 - 基于供需弹性、市场结构等因素评估影响程度
第五步：风险因素识别 - 识别可能的反向因素和不确定性
"""
    
    # 检查第一性原理关键词
    first_principles_keywords = [
        "第一性原理", "基础经济假设", "经济规律", "逻辑推导",
        "基础经济变量", "经济机制", "传导路径", "供需关系",
        "价格机制", "竞争机制", "替代机制"
    ]
    
    found_keywords = []
    for keyword in first_principles_keywords:
        if keyword in prompt_template:
            found_keywords.append(keyword)
    
    print(f"✅ 找到第一性原理关键词: {len(found_keywords)}/{len(first_principles_keywords)}")
    print(f"   关键词: {', '.join(found_keywords)}")
    
    # 检查是否包含分析框架
    framework_steps = ["第一步", "第二步", "第三步", "第四步", "第五步"]
    framework_found = all(step in prompt_template for step in framework_steps)
    
    if framework_found:
        print("✅ 五步分析框架完整")
    else:
        print("❌ 分析框架不完整")
    
    return len(found_keywords) >= 8 and framework_found


def test_glm_prompt_structure():
    """测试GLM提示词结构"""
    print("\n🧪 测试GLM提示词结构...")
    
    glm_client = GLMClient()
    
    # 调用_build_analysis_prompt方法
    test_title = "测试新闻标题"
    test_content = "测试新闻内容"
    
    prompt = glm_client._build_analysis_prompt(test_title, test_content)
    
    # 检查第一性原理关键词
    first_principles_keywords = [
        "第一性原理", "基础经济假设", "经济规律", "逻辑推导",
        "基础经济变量", "经济机制", "传导路径", "供需关系",
        "价格机制", "竞争机制", "替代机制"
    ]
    
    found_keywords = []
    for keyword in first_principles_keywords:
        if keyword in prompt:
            found_keywords.append(keyword)
    
    print(f"✅ 找到第一性原理关键词: {len(found_keywords)}/{len(first_principles_keywords)}")
    print(f"   关键词: {', '.join(found_keywords)}")
    
    # 检查是否包含分析框架
    framework_steps = ["第一步", "第二步", "第三步", "第四步", "第五步"]
    framework_found = all(step in prompt for step in framework_steps)
    
    if framework_found:
        print("✅ 五步分析框架完整")
    else:
        print("❌ 分析框架不完整")
    
    # 检查JSON输出格式要求
    json_fields = [
        "overall_impact", "us_market", "a_share_market", "hk_market",
        "major_indices", "key_stocks", "risk_assessment", "investment_advice"
    ]
    
    json_format_complete = all(field in prompt for field in json_fields)
    
    if json_format_complete:
        print("✅ JSON输出格式完整")
    else:
        print("❌ JSON输出格式不完整")
    
    return len(found_keywords) >= 8 and framework_found and json_format_complete


def test_prompt_consistency():
    """测试GLM和Gemini提示词一致性"""
    print("\n🧪 测试提示词一致性...")
    
    # 检查两个提示词是否包含相同的核心概念
    core_concepts = [
        "第一性原理", "基础经济假设", "分析框架",
        "事件本质分析", "经济机制识别", "传导路径分析",
        "量化评估", "风险因素识别"
    ]
    
    glm_client = GLMClient()
    glm_prompt = glm_client._build_analysis_prompt("测试", "测试")
    
    # 模拟Gemini提示词内容（从代码中提取的关键部分）
    gemini_concepts = [
        "第一性原理方法", "基础经济假设", "分析框架",
        "事件本质分析", "经济机制识别", "传导路径分析",
        "量化评估", "风险因素识别"
    ]
    
    consistency_score = 0
    for concept in core_concepts:
        if concept in glm_prompt:
            consistency_score += 1
    
    consistency_percentage = (consistency_score / len(core_concepts)) * 100
    
    print(f"✅ 核心概念一致性: {consistency_score}/{len(core_concepts)} ({consistency_percentage:.1f}%)")
    
    return consistency_percentage >= 80


def main():
    """主函数"""
    print("🚀 开始提示词结构测试")
    print("=" * 50)
    
    tests = [
        ("Gemini提示词结构", test_gemini_prompt_structure),
        ("GLM提示词结构", test_glm_prompt_structure),
        ("提示词一致性", test_prompt_consistency)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有提示词结构测试通过！")
        print("✅ 第一性原理重构成功应用到提示词中")
        return True
    else:
        print("⚠️  部分测试失败，需要检查提示词结构")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
