#!/usr/bin/env python3
"""
第一性原理新闻影响分析功能测试

测试重构后的快速分析功能是否：
1. 保持完全的向后兼容性
2. 正确应用第一性原理分析方法
3. 输出格式符合预期
4. GLM和Gemini模型切换正常
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.ai.llm import LLMManager
from backend.ai.llms.glm_client import GLMClient
from backend.services.news.news_impact_analyzer import NewsImpactAnalyzer


class FirstPrinciplesAnalysisTest:
    """第一性原理分析测试类"""
    
    def __init__(self):
        self.llm_manager = LLMManager()
        self.glm_client = GLMClient()
        self.analyzer = NewsImpactAnalyzer()
        
    def validate_json_structure(self, analysis: Dict[str, Any]) -> bool:
        """验证JSON输出结构是否符合预期"""
        required_fields = [
            'overall_impact',
            'us_market',
            'a_share_market', 
            'hk_market',
            'major_indices',
            'key_stocks',
            'risk_assessment',
            'investment_advice'
        ]
        
        # 检查顶级字段
        for field in required_fields:
            if field not in analysis:
                print(f"❌ 缺少必需字段: {field}")
                return False
        
        # 检查overall_impact结构
        overall = analysis.get('overall_impact', {})
        if not all(key in overall for key in ['level', 'summary']):
            print("❌ overall_impact结构不完整")
            return False
            
        # 检查市场分析结构
        for market in ['us_market', 'a_share_market', 'hk_market']:
            market_data = analysis.get(market, {})
            required_market_fields = ['impact_level', 'direction', 'affected_sectors', 'recommended_stocks', 'analysis']
            if not all(key in market_data for key in required_market_fields):
                print(f"❌ {market}结构不完整")
                return False
                
        # 检查major_indices结构
        indices = analysis.get('major_indices', {})
        required_indices = ['sp500', 'nasdaq', 'shanghai_composite', 'shenzhen_component', 'hang_seng']
        if not all(index in indices for index in required_indices):
            print("❌ major_indices结构不完整")
            return False
            
        print("✅ JSON结构验证通过")
        return True
    
    def validate_first_principles_content(self, analysis: Dict[str, Any]) -> bool:
        """验证是否体现第一性原理分析方法"""
        first_principles_keywords = [
            '基础变量', '经济机制', '传导路径', '供需关系', '价格机制',
            '第一性原理', '逻辑推导', '经济理论', '基础经济变量'
        ]
        
        # 检查整体分析摘要
        summary = analysis.get('overall_impact', {}).get('summary', '')
        has_first_principles = any(keyword in summary for keyword in first_principles_keywords)
        
        if not has_first_principles:
            # 检查市场分析内容
            for market in ['us_market', 'a_share_market', 'hk_market']:
                market_analysis = analysis.get(market, {}).get('analysis', '')
                if any(keyword in market_analysis for keyword in first_principles_keywords):
                    has_first_principles = True
                    break
                    
                # 检查推荐股票的理由
                stocks = analysis.get(market, {}).get('recommended_stocks', [])
                for stock in stocks:
                    reason = stock.get('reason', '')
                    if any(keyword in reason for keyword in first_principles_keywords):
                        has_first_principles = True
                        break
                        
                if has_first_principles:
                    break
        
        if has_first_principles:
            print("✅ 第一性原理内容验证通过")
            return True
        else:
            print("❌ 未发现第一性原理分析内容")
            return False
    
    async def test_gemini_analysis(self) -> bool:
        """测试Gemini模型分析"""
        print("\n🧪 测试Gemini模型分析...")
        
        if not self.llm_manager.is_available():
            print("⚠️  Gemini模型不可用，跳过测试")
            return True
            
        test_news = {
            'title': '美联储宣布加息25个基点',
            'content': '美联储在最新的货币政策会议上决定将联邦基金利率上调25个基点至5.25%-5.50%区间，这是今年第三次加息。美联储主席表示，此次加息是为了应对持续的通胀压力。'
        }
        
        try:
            result = await self.llm_manager.analyze_news_impact_with_gemini(
                test_news['title'], 
                test_news['content']
            )
            
            if not result.get('success'):
                print(f"❌ Gemini分析失败: {result.get('error')}")
                return False
                
            analysis = result.get('analysis', {})
            
            # 验证JSON结构
            if not self.validate_json_structure(analysis):
                return False
                
            # 验证第一性原理内容
            if not self.validate_first_principles_content(analysis):
                return False
                
            print("✅ Gemini模型测试通过")
            return True
            
        except Exception as e:
            print(f"❌ Gemini测试异常: {e}")
            return False
    
    async def test_glm_analysis(self) -> bool:
        """测试GLM模型分析"""
        print("\n🧪 测试GLM模型分析...")
        
        if not self.glm_client.is_available():
            print("⚠️  GLM模型不可用，跳过测试")
            return True
            
        test_news = {
            'title': '特斯拉发布新一代电池技术',
            'content': '特斯拉在电池日活动中发布了新一代4680电池技术，该技术将电池能量密度提升50%，成本降低56%。新技术预计将在2024年开始大规模量产。'
        }
        
        try:
            result = await self.glm_client.analyze_news_impact(
                test_news['title'], 
                test_news['content']
            )
            
            if not result.get('success'):
                print(f"❌ GLM分析失败: {result.get('error')}")
                return False
                
            analysis = result.get('analysis', {})
            
            # 验证JSON结构
            if not self.validate_json_structure(analysis):
                return False
                
            # 验证第一性原理内容
            if not self.validate_first_principles_content(analysis):
                return False
                
            print("✅ GLM模型测试通过")
            return True
            
        except Exception as e:
            print(f"❌ GLM测试异常: {e}")
            return False
    
    async def test_model_switching(self) -> bool:
        """测试模型切换功能"""
        print("\n🧪 测试模型切换功能...")
        
        test_news = {
            'title': '中国央行降准50个基点',
            'content': '中国人民银行宣布下调金融机构存款准备金率0.5个百分点，释放长期资金约1万亿元。此次降准旨在保持流动性合理充裕，支持实体经济发展。',
            'source': '央行官网',
            'publish_time': '2024-01-15 10:00:00'
        }
        
        try:
            # 测试GLM模型
            glm_result = await self.analyzer.analyze_news(test_news, model='glm')
            if not glm_result.get('success'):
                print(f"❌ GLM模型切换测试失败: {glm_result.get('error')}")
                return False
                
            # 测试Gemini模型（如果可用）
            if self.llm_manager.is_available():
                gemini_result = await self.analyzer.analyze_news(test_news, model='gemini')
                if not gemini_result.get('success'):
                    print(f"❌ Gemini模型切换测试失败: {gemini_result.get('error')}")
                    return False
                    
            print("✅ 模型切换功能测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 模型切换测试异常: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🚀 开始第一性原理新闻影响分析功能测试")
        print("=" * 60)
        
        tests = [
            self.test_gemini_analysis,
            self.test_glm_analysis,
            self.test_model_switching
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if await test():
                    passed += 1
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！第一性原理重构成功")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False


async def main():
    """主函数"""
    tester = FirstPrinciplesAnalysisTest()
    success = await tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
