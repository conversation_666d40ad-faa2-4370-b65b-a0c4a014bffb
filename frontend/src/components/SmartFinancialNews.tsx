'use client';

import React, { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';
import ErrorDisplay from './ErrorDisplay';
import NewsImpactAnalysis from './NewsImpactAnalysis';
import BatchAnalysisProgress from './BatchAnalysisProgress';
import DeepAnalysisProgress from './DeepAnalysisProgress';

interface NewsItem {
  id: number;
  title: string;
  content: string;
  source: string;
  source_name: string;
  publish_time: string;
  url?: string;
  category?: string;
  created_at: string;
}

interface SmartFinancialNewsProps {
  className?: string;
}

const SmartFinancialNews: React.FC<SmartFinancialNewsProps> = ({ className = '' }) => {
  const [newsList, setNewsList] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedNews, setSelectedNews] = useState<NewsItem | null>(null);
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [mounted, setMounted] = useState(false);
  const [selectedModel, setSelectedModel] = useState<'glm' | 'gemini'>('glm');
  const [selectedDeepModel, setSelectedDeepModel] = useState<'glm' | 'gemini'>('gemini'); // 深度分析模型选择
  const [isAnalyzing, setIsAnalyzing] = useState<number | null>(null);
  const [selectedNewsIds, setSelectedNewsIds] = useState<Set<number>>(new Set());
  const [showBatchAnalysis, setShowBatchAnalysis] = useState(false);
  const [batchTaskId, setBatchTaskId] = useState<string | null>(null);
  const [isDeepAnalyzing, setIsDeepAnalyzing] = useState<number | null>(null);
  const [showDeepAnalysis, setShowDeepAnalysis] = useState(false);

  const [selectedDeepAnalysisNews, setSelectedDeepAnalysisNews] = useState<NewsItem | null>(null);
  const [useFourLayerAnalysis, setUseFourLayerAnalysis] = useState(false);

  useEffect(() => {
    setMounted(true);
    setLastRefresh(new Date());
    fetchLatestNews();

    // 加载用户偏好的模型选择
    const savedModel = localStorage.getItem('preferred-ai-model') as 'glm' | 'gemini' | null;
    if (savedModel && (savedModel === 'glm' || savedModel === 'gemini')) {
      setSelectedModel(savedModel);
    }

    // 加载用户偏好的深度分析模型选择
    const savedDeepModel = localStorage.getItem('preferred-deep-analysis-model') as 'glm' | 'gemini' | null;
    if (savedDeepModel && (savedDeepModel === 'glm' || savedDeepModel === 'gemini')) {
      setSelectedDeepModel(savedDeepModel);
    }

    // 加载用户偏好的四层分析设置
    const savedFourLayerSetting = localStorage.getItem('use-four-layer-analysis');
    if (savedFourLayerSetting === 'true') {
      setUseFourLayerAnalysis(true);
    }
  }, []);

  // 保存用户模型偏好
  const handleModelChange = (model: 'glm' | 'gemini') => {
    setSelectedModel(model);
    localStorage.setItem('preferred-ai-model', model);
  };

  // 保存用户深度分析模型偏好
  const handleDeepModelChange = (model: 'glm' | 'gemini') => {
    setSelectedDeepModel(model);
    localStorage.setItem('preferred-deep-analysis-model', model);
  };

  // 切换四层分析设置
  const handleFourLayerAnalysisToggle = () => {
    const newValue = !useFourLayerAnalysis;
    setUseFourLayerAnalysis(newValue);
    localStorage.setItem('use-four-layer-analysis', newValue.toString());
  };

  // 批量分析处理函数
  const handleBatchAnalysis = async () => {
    if (selectedNewsIds.size === 0) {
      alert('请先选择要分析的新闻');
      return;
    }

    try {
      // 创建批量分析任务
      const response = await fetch('http://localhost:8000/news/batch-analysis/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          news_ids: Array.from(selectedNewsIds),
          model: selectedModel,
          priority: 'normal'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setBatchTaskId(data.task_id);
        setShowBatchAnalysis(true);

        // 启动任务
        await fetch(`http://localhost:8000/news/batch-analysis/${data.task_id}/start`, {
          method: 'POST',
        });
      } else {
        throw new Error(data.error || '创建批量分析任务失败');
      }
    } catch (error) {
      console.error('批量分析失败:', error);
      alert('批量分析失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  // 切换新闻选择状态
  const toggleNewsSelection = (newsId: number) => {
    const newSelection = new Set(selectedNewsIds);
    if (newSelection.has(newsId)) {
      newSelection.delete(newsId);
    } else {
      newSelection.add(newsId);
    }
    setSelectedNewsIds(newSelection);
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedNewsIds.size === newsList.length) {
      setSelectedNewsIds(new Set());
    } else {
      setSelectedNewsIds(new Set(newsList.map(news => news.id)));
    }
  };

  // 批量分析完成回调
  const handleBatchAnalysisComplete = (results: any) => {
    console.log('批量分析完成:', results);
    setShowBatchAnalysis(false);
    setBatchTaskId(null);
    setSelectedNewsIds(new Set());
    // 可以在这里刷新新闻列表或显示结果
    fetchLatestNews();
  };

  // 批量分析错误回调
  const handleBatchAnalysisError = (error: string) => {
    console.error('批量分析错误:', error);
    alert('批量分析失败: ' + error);
    setShowBatchAnalysis(false);
    setBatchTaskId(null);
  };

  // 深度分析处理函数
  const handleDeepAnalyzeNews = async (news: NewsItem) => {
    setIsDeepAnalyzing(news.id);
    setSelectedDeepAnalysisNews(news);

    // 直接显示分析界面，使用直接流式模式
    setShowDeepAnalysis(true);
    setIsDeepAnalyzing(null);
  };

  // 深度分析完成回调
  const handleDeepAnalysisComplete = (result: any) => {
    console.log('深度分析完成:', result);
    // 不自动关闭分析界面，让用户查看结果
    // setShowDeepAnalysis(false);
    // setDeepAnalysisTaskId(null);
    // setSelectedDeepAnalysisNews(null);
    // 分析完成后，结果会在DeepAnalysisProgress组件中显示
    // 用户可以手动点击"关闭"按钮关闭界面
  };

  // 深度分析错误回调
  const handleDeepAnalysisError = (error: string) => {
    console.error('深度分析错误:', error);
    alert('深度分析失败: ' + error);
    setShowDeepAnalysis(false);
    setSelectedDeepAnalysisNews(null);
  };

  // 关闭深度分析
  const handleCloseDeepAnalysis = () => {
    setShowDeepAnalysis(false);
    setSelectedDeepAnalysisNews(null);
  };

  const fetchLatestNews = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8000/financial-news/latest?limit=20');
      const data = await response.json();

      if (data.success) {
        setNewsList(data.data || []);
        setLastRefresh(new Date());
      } else {
        setError('获取新闻失败');
      }
    } catch (err) {
      setError('网络错误，请稍后重试');
      console.error('获取新闻失败:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalyzeNews = async (news: NewsItem) => {
    setIsAnalyzing(news.id);
    setSelectedNews(news);
    setShowAnalysis(true);

    // 分析完成后清除加载状态
    setTimeout(() => {
      setIsAnalyzing(null);
    }, 1000);
  };

  const handleCloseAnalysis = () => {
    setShowAnalysis(false);
    setSelectedNews(null);
  };

  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const formatLastRefresh = (date: Date | null) => {
    if (!date) return '加载中...';
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const getSourceColor = (source: string) => {
    const colors: { [key: string]: string } = {
      'sina': 'bg-red-50 text-red-700',
      '10jqka': 'bg-blue-50 text-blue-700',
      'fenghuang': 'bg-orange-50 text-orange-700',
      'jinrongjie': 'bg-green-50 text-green-700',
      'yuncaijing': 'bg-purple-50 text-purple-700',
      'eastmoney_breakfast': 'bg-yellow-50 text-yellow-700',
      'eastmoney_global': 'bg-indigo-50 text-indigo-700',
      'futu_global': 'bg-pink-50 text-pink-700',
      'ths_global': 'bg-cyan-50 text-cyan-700',
      'cls_global': 'bg-emerald-50 text-emerald-700',
      'sina_broker': 'bg-rose-50 text-rose-700'
    };
    return colors[source] || 'bg-gray-50 text-gray-700';
  };

  // 显示深度分析进度
  if (showDeepAnalysis && selectedDeepAnalysisNews) {
    return (
      <div className={`smart-financial-news ${className}`}>
        <DeepAnalysisProgress
          newsData={{
            id: selectedDeepAnalysisNews.id,
            title: selectedDeepAnalysisNews.title,
            content: selectedDeepAnalysisNews.content,
            source: selectedDeepAnalysisNews.source_name,
            publish_time: selectedDeepAnalysisNews.publish_time
          }}
          onComplete={handleDeepAnalysisComplete}
          onError={handleDeepAnalysisError}
          onClose={handleCloseDeepAnalysis}
          useFourLayerAnalysis={useFourLayerAnalysis}
          useDirectStream={true}
          model={selectedDeepModel}
        />
      </div>
    );
  }

  // 显示批量分析进度
  if (showBatchAnalysis && batchTaskId) {
    return (
      <div className={`smart-financial-news ${className}`}>
        <div className="mb-4">
          <button
            onClick={() => {
              setShowBatchAnalysis(false);
              setBatchTaskId(null);
            }}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>返回新闻列表</span>
          </button>
        </div>
        <BatchAnalysisProgress
          taskId={batchTaskId}
          onComplete={handleBatchAnalysisComplete}
          onError={handleBatchAnalysisError}
        />
      </div>
    );
  }

  if (showAnalysis && selectedNews) {
    return (
      <div className={`smart-financial-news ${className}`}>
        <NewsImpactAnalysis
          newsData={{
            id: selectedNews.id,
            title: selectedNews.title,
            content: selectedNews.content,
            source: selectedNews.source_name,
            publish_time: selectedNews.publish_time
          }}
          model={selectedModel}
          onClose={handleCloseAnalysis}
        />
      </div>
    );
  }

  return (
    <div className={`smart-financial-news ${className}`}>
      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">🤖 智能财经资讯</h2>
          <p className="text-gray-600">AI驱动的新闻影响分析</p>
        </div>
        <div className="flex items-center space-x-4">
          {/* AI Model Selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">快速分析:</span>
            <select
              value={selectedModel}
              onChange={(e) => handleModelChange(e.target.value as 'glm' | 'gemini')}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="glm">GLM-4-Flash</option>
              <option value="gemini">Gemini-2.0</option>
            </select>
          </div>

          {/* Deep Analysis Model Selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">深度分析:</span>
            <select
              value={selectedDeepModel}
              onChange={(e) => handleDeepModelChange(e.target.value as 'glm' | 'gemini')}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="glm">GLM-4-Flash</option>
              <option value="gemini">Gemini-2.0</option>
            </select>
          </div>

          {/* 四层思维链分析开关 */}
          <div className="flex items-center space-x-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={useFourLayerAnalysis}
                onChange={handleFourLayerAnalysisToggle}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">🧠 四层分析</span>
            </label>
            {useFourLayerAnalysis && (
              <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-md font-medium">
                已启用
              </span>
            )}
          </div>

          <span className="text-sm text-gray-500">
            最后更新: {mounted ? formatLastRefresh(lastRefresh) : '加载中...'}
          </span>

          {/* 批量分析按钮 */}
          {selectedNewsIds.size > 0 && (
            <button
              onClick={handleBatchAnalysis}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <span>批量分析 ({selectedNewsIds.size})</span>
            </button>
          )}

          <button
            onClick={fetchLatestNews}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-2"
          >
            {isLoading ? (
              <LoadingSpinner size="sm" color="white" />
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            )}
            <span>刷新</span>
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && !isLoading && (
        <div className="mb-6">
          <ErrorDisplay 
            error={error}
            onRetry={fetchLatestNews}
          />
        </div>
      )}

      {/* Loading State */}
      {isLoading && newsList.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="text-gray-600 mt-4">正在加载最新财经资讯...</p>
          </div>
        </div>
      )}

      {/* 批量操作控制栏 */}
      {!isLoading && newsList.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4 mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={selectedNewsIds.size === newsList.length && newsList.length > 0}
                onChange={toggleSelectAll}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                全选 ({selectedNewsIds.size}/{newsList.length})
              </span>
            </label>
            {selectedNewsIds.size > 0 && (
              <span className="text-sm text-blue-600">
                已选择 {selectedNewsIds.size} 条新闻
              </span>
            )}
          </div>
          {selectedNewsIds.size > 0 && (
            <button
              onClick={() => setSelectedNewsIds(new Set())}
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
            >
              清除选择
            </button>
          )}
        </div>
      )}

      {/* News List */}
      {!isLoading && newsList.length > 0 && (
        <div className="space-y-4">
          {newsList.map((news) => (
            <div key={news.id} className={`bg-white rounded-lg border p-6 hover:shadow-md transition-all ${
              selectedNewsIds.has(news.id) ? 'border-blue-300 bg-blue-50' : 'border-gray-200'
            }`}>
              {/* News Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-start space-x-3 flex-1">
                  {/* 选择框 */}
                  <input
                    type="checkbox"
                    checked={selectedNewsIds.has(news.id)}
                    onChange={() => toggleNewsSelection(news.id)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
                  />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
                      {news.title}
                    </h3>
                    <div className="flex items-center space-x-3 text-sm text-gray-500">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getSourceColor(news.source)}`}>
                        {news.source_name}
                      </span>
                      <span>{formatTime(news.publish_time)}</span>
                      {news.category && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                          {news.category}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* News Content */}
              <div className="mb-4">
                <p className="text-gray-700 leading-relaxed">
                  {truncateContent(news.content)}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleAnalyzeNews(news)}
                    disabled={isAnalyzing === news.id}
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2 font-medium"
                  >
                    {isAnalyzing === news.id ? (
                      <LoadingSpinner size="sm" color="white" />
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    )}
                    <span>
                      🤖 {isAnalyzing === news.id ? '快速分析中...' : `AI快速分析 (${selectedModel.toUpperCase()})`}
                    </span>
                  </button>

                  <button
                    onClick={() => handleDeepAnalyzeNews(news)}
                    disabled={isDeepAnalyzing === news.id}
                    className="px-4 py-2 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-lg hover:from-green-600 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2 font-medium"
                  >
                    {isDeepAnalyzing === news.id ? (
                      <LoadingSpinner size="sm" color="white" />
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    )}
                    <span>
                      🔍 {isDeepAnalyzing === news.id ? '深度分析中...' : `AI深度分析 (${selectedDeepModel.toUpperCase()})`}
                    </span>
                  </button>
                  
                  {news.url && (
                    <a
                      href={news.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                      <span>查看原文</span>
                    </a>
                  )}
                </div>

                <div className="text-xs text-gray-400">
                  入库时间: {formatTime(news.created_at)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && newsList.length === 0 && !error && (
        <div className="text-center py-12">
          <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无新闻</h3>
          <p className="text-gray-500">当前没有可显示的财经新闻，请稍后刷新</p>
        </div>
      )}

      {/* Feature Info */}
      <div className="mt-8 space-y-4">
        <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-blue-900">🤖 AI 快速分析功能</h4>
              <p className="text-sm text-blue-700 mt-1">
                点击"AI快速分析"按钮，获取基于{selectedModel === 'glm' ? 'GLM-4-Flash' : 'Gemini-2.0'}的专业市场影响分析，包括美股、A股、港股市场影响评估和投资建议。
              </p>
            </div>
          </div>
        </div>
        
        <div className="p-4 bg-gradient-to-r from-green-50 to-teal-50 border border-green-200 rounded-lg">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-green-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-green-900">🔍 AI 深度分析功能</h4>
              <p className="text-sm text-green-700 mt-1">
                点击"AI深度分析"按钮，基于{selectedDeepModel === 'glm' ? 'GLM-4-Flash' : 'Gemini-2.0'}进行网络搜索研究，提供深度背景分析、行业影响评估和多角度市场洞察。
                {useFourLayerAnalysis && (
                  <span className="block mt-1 text-blue-700 font-medium">
                    🧠 已启用四层思维链：从事件感知→深度挖掘→国内影响→标的筛选，挖掘专业认知信息差。
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartFinancialNews; 