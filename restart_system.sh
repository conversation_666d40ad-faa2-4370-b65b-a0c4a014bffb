#!/bin/bash
# AI新闻分析系统 - 重启脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🔄 重启AI新闻分析系统"
echo "================================"

# 检查脚本是否存在
if [ ! -f "./stop_system.sh" ]; then
    log_error "未找到停止脚本 ./stop_system.sh"
    exit 1
fi

if [ ! -f "./start_system.sh" ]; then
    log_error "未找到启动脚本 ./start_system.sh"
    exit 1
fi

# 停止现有服务
log_info "停止现有服务..."
./stop_system.sh

# 等待一段时间确保服务完全停止
log_info "等待服务完全停止..."
sleep 3

# 启动服务
log_info "启动服务..."
./start_system.sh "$@"
