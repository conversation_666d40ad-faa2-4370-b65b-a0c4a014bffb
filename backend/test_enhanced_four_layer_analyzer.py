#!/usr/bin/env python3
"""
测试优化后的四层漏斗思维链分析器
验证是否符合用户要求的详细分析框架
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.ai.deep_research.core.deep_research_engine import DeepResearchEngine

# 测试新闻数据
TEST_NEWS_DATA = {
    "title": "美国对华芯片制裁升级，限制先进制程设备出口",
    "content": """
    美国商务部今日宣布，将进一步收紧对中国半导体行业的出口管制，
    禁止向中国出口用于生产14纳米以下先进制程芯片的关键设备。
    此次制裁涉及光刻机、蚀刻设备、薄膜沉积设备等核心制造装备，
    预计将对中国半导体产业链造成重大冲击。
    业内人士预计，这将加速中国半导体设备国产化进程，
    同时可能推高全球芯片价格。
    """,
    "source": "路透社",
    "publish_time": "2025-06-20 09:30:00",
    "category": "科技政策"
}

async def test_enhanced_four_layer_analyzer():
    """测试优化后的四层分析器"""
    
    print("🚀 开始测试优化后的四层漏斗思维链分析器")
    print("=" * 80)
    print(f"📰 测试新闻：{TEST_NEWS_DATA['title']}")
    print(f"📅 发布时间：{TEST_NEWS_DATA['publish_time']}")
    print(f"📊 新闻来源：{TEST_NEWS_DATA['source']}")
    print("=" * 80)
    
    try:
        # 创建深度研究引擎
        engine = DeepResearchEngine()
        
        # 配置分析参数
        analysis_config = {
            "use_four_layer_analysis": True,
            "max_research_loops": 1,
            "initial_search_query_count": 3,
            "enable_detailed_analysis": True
        }
        
        print("🔄 开始执行四层漏斗分析...")
        print()
        
        # 执行分析并收集结果
        analysis_results = []
        layer_results = {}
        
        async for result in engine.analyze_news_deep(
            news_data=TEST_NEWS_DATA,
            analysis_config=analysis_config
        ):
            result_type = result.get("type", "unknown")
            message = result.get("message", "")
            timestamp = result.get("timestamp", "")
            
            # 打印进度信息
            if result_type == "task_started":
                print(f"✅ {message}")
                print(f"   任务ID: {result.get('task_id')}")
                
            elif result_type == "context_completed":
                print(f"🔍 {message}")
                
            elif result_type == "research_completed":
                print(f"📚 {message}")
                research_count = result.get("research_count", 0)
                print(f"   完成研究数量: {research_count}")
                
            elif result_type == "four_layer_analysis_started":
                print(f"🧠 {message}")
                
            elif result_type == "llm_call_starting":
                model = result.get("model", "")
                step = result.get("step", "")
                print(f"🤖 {message}")
                print(f"   模型: {model}, 步骤: {step}")
                
            elif result_type == "four_layer_completed":
                print(f"✅ {message}")
                print()
                print("=" * 80)
                print("📊 四层漏斗思维链分析结果")
                print("=" * 80)
                
                # 保存完整结果
                layer_results = {
                    "analysis_summary": result.get("analysis_summary", ""),
                    "investment_targets": result.get("investment_targets", []),
                    "confidence": result.get("confidence", 0)
                }
                
                # 显示分析摘要
                summary = result.get("analysis_summary", "")
                if summary:
                    print("📋 分析摘要:")
                    print(summary)
                    print()
                
                # 显示投资标的
                targets = result.get("investment_targets", [])
                if targets:
                    print("🎯 投资标的推荐:")
                    for i, target in enumerate(targets, 1):
                        print(f"   {i}. {target.get('name', '')} ({target.get('symbol', '')})")
                        print(f"      类型: {target.get('target_type', '')}")
                        print(f"      推荐理由: {target.get('recommendation_reason', '')}")
                        print(f"      预期收益: {target.get('expected_return', '')}")
                        print(f"      时间框架: {target.get('time_frame', '')}")
                        print(f"      置信度: {target.get('confidence', 0):.1%}")
                        print()
                
                confidence = result.get("confidence", 0)
                print(f"📈 整体置信度: {confidence:.1%}")
                
            elif result_type == "analysis_completed":
                print(f"🎉 {message}")

                # 保存最终结果
                complete_result = result.get("result", {})
                final_analysis = complete_result.get("final_analysis", {})
                analysis_results.append({
                    "timestamp": timestamp,
                    "complete_result": complete_result,
                    "final_analysis": final_analysis,
                    "layer_results": layer_results
                })
                
            elif result_type == "four_layer_fallback":
                print(f"⚠️ {message}")
                
            else:
                # 其他类型的结果
                if message:
                    print(f"ℹ️ {message}")
        
        print()
        print("=" * 80)
        print("📊 分析质量评估")
        print("=" * 80)
        
        # 评估分析质量
        if analysis_results:
            final_result = analysis_results[-1]
            final_analysis = final_result.get("final_analysis", {})
            complete_result = final_result.get("complete_result", {})

            print(f"📊 完整结果键: {list(complete_result.keys())}")
            print(f"📊 最终分析键: {list(final_analysis.keys())}")

            # 检查是否包含四层分析结果
            has_layer1 = "layer1_analysis" in final_analysis
            has_layer2 = "layer2_analysis" in final_analysis
            has_layer3 = "layer3_analysis" in final_analysis
            has_layer4 = "layer4_analysis" in final_analysis

            print(f"✅ 第一层分析: {'完成' if has_layer1 else '缺失'}")
            print(f"✅ 第二层分析: {'完成' if has_layer2 else '缺失'}")
            print(f"✅ 第三层分析: {'完成' if has_layer3 else '缺失'}")
            print(f"✅ 第四层分析: {'完成' if has_layer4 else '缺失'}")

            # 检查投资标的质量
            targets = final_analysis.get("investment_targets", [])
            print(f"🎯 投资标的数量: {len(targets)}")

            if targets:
                core_targets = [t for t in targets if t.get("target_type") == "核心推荐"]
                backup_targets = [t for t in targets if t.get("target_type") == "备选推荐"]
                print(f"   核心推荐: {len(core_targets)}个")
                print(f"   备选推荐: {len(backup_targets)}个")

                # 显示标的详情
                for i, target in enumerate(targets[:3], 1):
                    print(f"   标的{i}: {target.get('name', '')} ({target.get('symbol', '')})")
                    print(f"      类型: {target.get('target_type', '')}")
                    print(f"      置信度: {target.get('confidence', 0):.1%}")

            # 整体置信度
            confidence = final_analysis.get("confidence", 0)
            print(f"📈 整体置信度: {confidence:.1%}")

            # 分析类型确认
            analysis_type = final_analysis.get("analysis_type", "")
            print(f"🔍 分析类型: {analysis_type}")

            # 检查四层分析内容长度
            if has_layer1:
                layer1_length = len(final_analysis.get("layer1_analysis", ""))
                print(f"📝 第一层分析长度: {layer1_length} 字符")
            if has_layer2:
                layer2_length = len(final_analysis.get("layer2_analysis", ""))
                print(f"📝 第二层分析长度: {layer2_length} 字符")
            if has_layer3:
                layer3_length = len(final_analysis.get("layer3_analysis", ""))
                print(f"📝 第三层分析长度: {layer3_length} 字符")
            if has_layer4:
                layer4_length = len(final_analysis.get("layer4_analysis", ""))
                print(f"📝 第四层分析长度: {layer4_length} 字符")

        else:
            print("❌ 未获得完整分析结果")
        
        print()
        print("=" * 80)
        print("✅ 四层漏斗思维链分析器测试完成")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_test_requirements():
    """打印测试要求说明"""
    
    requirements = """
    🎯 四层漏斗思维链分析器测试要求
    
    本测试验证优化后的分析器是否满足以下要求：
    
    📊 第一层：事件感知与直接联想
    ✓ 可信度评估（1-10分制，需说明评分依据）
    ✓ 提取核心要素：时间、地点、关键主体、影响范围、紧急程度
    ✓ 运用第一性原理推导直接影响路径（至少4条具体路径）
    ✓ 分析大众认知预期和盲点（识别"红海"领域）
    ✓ 提出5个具体的深挖方向
    
    🔍 第二层：深挖供应链与关键信息
    ✓ 识别核心受影响主体（包括被大众忽视的主体）
    ✓ 解构全球供应链角色，重点关注"不为人知的关键商品"
    ✓ 追溯贸易伙伴关系，量化贸易依存度
    ✓ 提供影响程度的量化分析（具体数据、替代方案、供需缺口时间）
    ✓ 总结关键信息差发现，为第三层分析提供6个具体线索
    
    🏭 第三层：聚焦国内产业与市场动态
    ✓ 识别受影响的具体行业和细分领域（直接影响+间接影响）
    ✓ 进行供需变化的量化分析（供给端、需求端、供需平衡）
    ✓ 分析价格传导机制和预期涨幅（具体百分比预测）
    ✓ 为第四层公司筛选提供明确标准
    
    🎯 第四层：筛选与锁定具体上市公司
    ✓ 建立候选标的池（A股、港股、美股各至少3个标的）
    ✓ 进行精准画像分析：业务占比、产能规模、业绩弹性、财务健康度
    ✓ 建立综合评分体系并排序（权重分配要明确）
    ✓ 进行风险收益评估（潜在收益空间、主要风险因素、风险调整后收益预期）
    
    📈 输出质量要求
    ✓ 每层分析必须包含具体的数据、案例和量化指标
    ✓ 避免空洞的列表项，每个要点都要有实质内容
    ✓ 最终输出包括：1-3个核心推荐标的、2-3个备选标的、整体分析置信度评估
    """
    
    print(requirements)

if __name__ == "__main__":
    print_test_requirements()
    print()
    
    # 运行测试
    success = asyncio.run(test_enhanced_four_layer_analyzer())
    
    if success:
        print("🎉 测试成功完成！")
        sys.exit(0)
    else:
        print("❌ 测试失败！")
        sys.exit(1)
