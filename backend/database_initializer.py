#!/usr/bin/env python3
"""
数据库初始化器
自动检测和创建所有必需的数据库文件和表结构
"""

import os
import sqlite3
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import json
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.migrations_dir = Path("migrations")
        
        # 数据库配置
        self.databases = {
            "financial_data.db": "金融数据库",
            "financial_news.db": "金融新闻数据库", 
            "users.db": "用户数据库",
            "news_impact_analysis.db": "新闻影响分析数据库",
            "divergence_data.db": "背离数据库"
        }
        
        # 初始化状态记录
        self.init_status = {
            "databases_created": [],
            "tables_created": [],
            "migrations_executed": [],
            "errors": []
        }
    
    def initialize_all(self) -> bool:
        """初始化所有数据库"""
        logger.info("🚀 开始数据库初始化...")
        
        try:
            # 1. 创建数据目录
            self._create_data_directory()
            
            # 2. 创建数据库文件
            self._create_database_files()
            
            # 3. 初始化表结构
            self._initialize_table_structures()
            
            # 4. 执行迁移脚本
            self._execute_migrations()
            
            # 5. 验证初始化结果
            success = self._verify_initialization()
            
            # 6. 显示初始化报告
            self._show_initialization_report()
            
            return success
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            self.init_status["errors"].append(str(e))
            return False
    
    def _create_data_directory(self):
        """创建数据目录"""
        if not self.data_dir.exists():
            self.data_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ 创建数据目录: {self.data_dir}")
        else:
            logger.info(f"✅ 数据目录已存在: {self.data_dir}")
    
    def _create_database_files(self):
        """创建数据库文件"""
        for db_file, description in self.databases.items():
            db_path = self.data_dir / db_file
            
            if not db_path.exists():
                # 创建空的数据库文件
                db_path.touch()
                logger.info(f"✅ 创建{description}: {db_file}")
                self.init_status["databases_created"].append(db_file)
            else:
                logger.info(f"✅ {description}已存在: {db_file}")
    
    def _initialize_table_structures(self):
        """初始化表结构"""
        # 初始化金融数据库
        self._init_financial_data_db()
        
        # 初始化新闻数据库
        self._init_financial_news_db()
        
        # 初始化用户数据库
        self._init_users_db()
        
        # 初始化新闻影响分析数据库
        self._init_news_impact_analysis_db()
        
        # 初始化背离数据库
        self._init_divergence_data_db()
    
    def _init_financial_data_db(self):
        """初始化金融数据库"""
        db_path = self.data_dir / "financial_data.db"
        
        with sqlite3.connect(db_path) as conn:
            # 股票基本信息表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    ts_code TEXT PRIMARY KEY,
                    symbol TEXT NOT NULL,
                    name TEXT,
                    market TEXT,
                    industry TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 中国A股日线数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cn_daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change_amount REAL,
                    pct_change REAL,
                    vol REAL,
                    amount REAL,
                    vwap REAL,
                    turnover_ratio REAL,
                    total_mv REAL,
                    pe REAL,
                    pb REAL,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """)
            
            # 美股日线数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS us_daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change_amount REAL,
                    pct_change REAL,
                    vol REAL,
                    amount REAL,
                    vwap REAL,
                    turnover_ratio REAL,
                    total_mv REAL,
                    pe REAL,
                    pb REAL,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """)
            
            # 因子数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS factor_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    factor_name TEXT NOT NULL,
                    factor_value REAL,
                    user_id INTEGER,
                    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date, factor_name)
                )
            """)
            
            # 用户关注列表表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_watchlist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    ts_code TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, ts_code)
                )
            """)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_cn_daily_data_ts_code ON cn_daily_data(ts_code)",
                "CREATE INDEX IF NOT EXISTS idx_cn_daily_data_trade_date ON cn_daily_data(trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_us_daily_data_ts_code ON us_daily_data(ts_code)",
                "CREATE INDEX IF NOT EXISTS idx_us_daily_data_trade_date ON us_daily_data(trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_factor_data_ts_code ON factor_data(ts_code)",
                "CREATE INDEX IF NOT EXISTS idx_factor_data_factor_name ON factor_data(factor_name)",
                "CREATE INDEX IF NOT EXISTS idx_user_watchlist_user_id ON user_watchlist(user_id)"
            ]
            
            for index_sql in indexes:
                conn.execute(index_sql)
            
            conn.commit()
            self.init_status["tables_created"].append("financial_data.db tables")
            logger.info("✅ 金融数据库表结构初始化完成")
    
    def _init_financial_news_db(self):
        """初始化新闻数据库"""
        db_path = self.data_dir / "financial_news.db"
        
        with sqlite3.connect(db_path) as conn:
            # 财经新闻表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS financial_news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    news_id VARCHAR(255) UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    content TEXT,
                    publish_time DATETIME NOT NULL,
                    source VARCHAR(100) NOT NULL,
                    source_name VARCHAR(100) NOT NULL,
                    url TEXT,
                    category VARCHAR(100),
                    user_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
            
            # 新闻影响分析表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS news_impact_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    news_id INTEGER NOT NULL,
                    analysis_result TEXT,
                    impact_score REAL,
                    affected_stocks TEXT,
                    analysis_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER,
                    FOREIGN KEY (news_id) REFERENCES financial_news (id)
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_financial_news_publish_time ON financial_news(publish_time)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_financial_news_source ON financial_news(source)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_news_impact_analysis_news_id ON news_impact_analysis(news_id)")
            
            conn.commit()
            self.init_status["tables_created"].append("financial_news.db tables")
            logger.info("✅ 新闻数据库表结构初始化完成")
    
    def _init_users_db(self):
        """初始化用户数据库"""
        db_path = self.data_dir / "users.db"
        
        with sqlite3.connect(db_path) as conn:
            # 用户表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 用户会话表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            
            conn.commit()
            self.init_status["tables_created"].append("users.db tables")
            logger.info("✅ 用户数据库表结构初始化完成")
    
    def _init_news_impact_analysis_db(self):
        """初始化新闻影响分析数据库"""
        db_path = self.data_dir / "news_impact_analysis.db"
        
        with sqlite3.connect(db_path) as conn:
            # 新闻深度研究表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS news_deep_research (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT UNIQUE NOT NULL,
                    news_id INTEGER,
                    news_title TEXT NOT NULL,
                    research_topic TEXT NOT NULL,
                    research_status TEXT DEFAULT 'pending',
                    priority_level TEXT DEFAULT 'medium',
                    sources_gathered TEXT DEFAULT '[]',
                    research_queries TEXT DEFAULT '[]',
                    final_analysis TEXT,
                    confidence_score REAL,
                    processing_time_seconds REAL,
                    error_message TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    completed_at DATETIME,
                    user_id INTEGER
                )
            """)
            
            # 深度研究队列表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS deep_research_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT NOT NULL,
                    priority INTEGER DEFAULT 5,
                    status TEXT DEFAULT 'pending',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    started_at DATETIME,
                    completed_at DATETIME,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 3
                )
            """)
            
            conn.commit()
            self.init_status["tables_created"].append("news_impact_analysis.db tables")
            logger.info("✅ 新闻影响分析数据库表结构初始化完成")
    
    def _init_divergence_data_db(self):
        """初始化背离数据库"""
        db_path = self.data_dir / "divergence_data.db"
        
        with sqlite3.connect(db_path) as conn:
            # 背离信号表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS divergence_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    market TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    detected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    price_at_signal REAL,
                    macd_value REAL,
                    signal_strength REAL,
                    chart_image BLOB,
                    user_id INTEGER
                )
            """)
            
            conn.execute("CREATE INDEX IF NOT EXISTS idx_divergence_signals_symbol ON divergence_signals(symbol)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_divergence_signals_detected_at ON divergence_signals(detected_at)")
            
            conn.commit()
            self.init_status["tables_created"].append("divergence_data.db tables")
            logger.info("✅ 背离数据库表结构初始化完成")
    
    def _execute_migrations(self):
        """执行迁移脚本"""
        if not self.migrations_dir.exists():
            logger.warning("⚠️  未找到migrations目录，跳过迁移脚本执行")
            return
        
        migration_files = list(self.migrations_dir.glob("*.sql"))
        if not migration_files:
            logger.info("ℹ️  未找到SQL迁移文件")
            return
        
        for migration_file in sorted(migration_files):
            try:
                self._execute_migration_file(migration_file)
                self.init_status["migrations_executed"].append(migration_file.name)
            except Exception as e:
                error_msg = f"执行迁移文件失败 {migration_file.name}: {e}"
                logger.error(error_msg)
                self.init_status["errors"].append(error_msg)
    
    def _execute_migration_file(self, migration_file: Path):
        """执行单个迁移文件"""
        # 根据文件名确定目标数据库
        file_name = migration_file.name.lower()
        
        if "financial_news" in file_name:
            target_db = self.data_dir / "financial_news.db"
        elif "user" in file_name:
            target_db = self.data_dir / "users.db"
        elif "deep_research" in file_name or "news_impact" in file_name:
            target_db = self.data_dir / "news_impact_analysis.db"
        elif "supabase" in file_name:
            logger.info(f"⏭️  跳过Supabase迁移文件: {migration_file.name}")
            return
        else:
            target_db = self.data_dir / "financial_data.db"
        
        logger.info(f"📄 执行迁移: {migration_file.name} -> {target_db.name}")
        
        with open(migration_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        with sqlite3.connect(target_db) as conn:
            # 分割SQL语句并逐个执行
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for statement in statements:
                if statement and not statement.startswith('--'):
                    try:
                        conn.execute(statement)
                    except sqlite3.Error as e:
                        # 忽略一些常见的无害错误
                        if "already exists" not in str(e).lower():
                            logger.warning(f"⚠️  SQL执行警告: {e}")
            
            conn.commit()
        
        logger.info(f"✅ 迁移完成: {migration_file.name}")
    
    def _verify_initialization(self) -> bool:
        """验证初始化结果"""
        logger.info("🔍 验证数据库初始化结果...")
        
        success = True
        
        # 检查数据库文件是否存在
        for db_file in self.databases.keys():
            db_path = self.data_dir / db_file
            if not db_path.exists():
                logger.error(f"❌ 数据库文件不存在: {db_file}")
                success = False
            else:
                # 检查数据库是否可以连接
                try:
                    with sqlite3.connect(db_path) as conn:
                        conn.execute("SELECT 1")
                    logger.info(f"✅ 数据库连接正常: {db_file}")
                except Exception as e:
                    logger.error(f"❌ 数据库连接失败 {db_file}: {e}")
                    success = False
        
        return success
    
    def _show_initialization_report(self):
        """显示初始化报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 数据库初始化报告")
        logger.info("="*60)
        
        logger.info(f"✅ 创建的数据库: {len(self.init_status['databases_created'])}")
        for db in self.init_status['databases_created']:
            logger.info(f"   - {db}")
        
        logger.info(f"✅ 初始化的表结构: {len(self.init_status['tables_created'])}")
        for table in self.init_status['tables_created']:
            logger.info(f"   - {table}")
        
        logger.info(f"✅ 执行的迁移: {len(self.init_status['migrations_executed'])}")
        for migration in self.init_status['migrations_executed']:
            logger.info(f"   - {migration}")
        
        if self.init_status['errors']:
            logger.warning(f"⚠️  错误数量: {len(self.init_status['errors'])}")
            for error in self.init_status['errors']:
                logger.warning(f"   - {error}")
        
        logger.info("="*60)

def main():
    """主函数"""
    initializer = DatabaseInitializer()
    success = initializer.initialize_all()
    
    if success:
        logger.info("🎉 数据库初始化成功完成！")
        return 0
    else:
        logger.error("❌ 数据库初始化失败！")
        return 1

if __name__ == "__main__":
    exit(main())
