"""
GLM API适配器

为深度研究提供GLM API支持，实现与GeminiAdapter相同的接口
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional

from ...llms.glm_client import get_glm_client
from ..core.state_manager import ResearchConfiguration

logger = logging.getLogger(__name__)


class GLMResearchAdapter:
    """GLM研究适配器"""
    
    def __init__(self):
        self.glm_client = get_glm_client()
        self.config = ResearchConfiguration()
        
    async def generate_research_queries(
        self,
        news_content: str,
        news_title: str,
        num_queries: int = 2,
        config: Optional[Dict] = None
    ) -> List[Dict[str, str]]:
        """
        生成研究查询
        
        Args:
            news_content: 新闻内容
            news_title: 新闻标题
            num_queries: 查询数量
            config: 运行配置
            
        Returns:
            查询列表，每个查询包含query和rationale
        """
        try:
            # 构建提示词
            prompt = self._build_query_generation_prompt(
                news_title, news_content, num_queries
            )
            
            # 使用GLM客户端进行分析
            response_data = await self.glm_client.analyze_with_web_search(prompt, temperature=1.0)
            
            if not response_data.get('success'):
                logger.warning(f"GLM查询生成失败: {response_data.get('error')}")
                return self._get_default_queries(news_title, num_queries)
            
            response = response_data.get('content', '')
            
            # 尝试解析JSON响应
            try:
                if response and isinstance(response, str):
                    # 如果响应包含代码块，提取JSON部分
                    if "```json" in response:
                        json_start = response.find("```json") + 7
                        json_end = response.find("```", json_start)
                        json_str = response[json_start:json_end].strip()
                    else:
                        json_str = response.strip()
                    
                    parsed = json.loads(json_str)
                    queries = parsed.get("queries", [])
                    
                    # 确保格式正确
                    formatted_queries = []
                    for query in queries:
                        if isinstance(query, dict):
                            formatted_queries.append(query)
                        elif isinstance(query, str):
                            formatted_queries.append({
                                "query": query,
                                "rationale": "用于深度研究的搜索查询"
                            })
                    
                    return formatted_queries[:num_queries]
                    
            except (json.JSONDecodeError, AttributeError) as e:
                logger.warning(f"解析GLM查询JSON失败: {e}, 使用默认查询")
            
            # 返回默认查询
            return self._get_default_queries(news_title, num_queries)
            
        except Exception as e:
            logger.error(f"GLM生成研究查询失败: {e}")
            return self._get_default_queries(news_title, num_queries)
    
    async def perform_web_search(
        self,
        search_query: str,
        session_id: int,
        config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        执行网络搜索研究
        
        Args:
            search_query: 搜索查询
            session_id: 会话ID
            config: 运行配置
            
        Returns:
            搜索结果，包含文本和引用信息
        """
        try:
            # 构建搜索提示词
            prompt = self._build_web_search_prompt(search_query)
            
            # 使用GLM客户端进行网络搜索
            response_data = await self.glm_client.analyze_with_web_search(prompt, temperature=0.3)
            
            if response_data.get('success'):
                response = response_data.get('content', '')
                return {
                    "text": response,
                    "sources": [],  # 简化版本暂不解析具体来源
                    "citations": [],
                    "original_text": response
                }
            else:
                logger.warning(f"GLM网络搜索失败: {response_data.get('error')}")
                return {
                    "text": f"未找到关于'{search_query}'的相关信息。",
                    "sources": [],
                    "citations": [],
                    "original_text": ""
                }
                
        except Exception as e:
            logger.error(f"GLM网络搜索失败: {e}")
            # 生成更详细的默认分析结果
            return {
                "text": f"基于现有知识对'{search_query}'的分析：该主题涉及重要的市场动态，建议关注相关行业发展趋势、监管政策变化以及对投资组合的潜在影响。投资者应当谨慎评估风险并考虑专业建议。",
                "sources": [],
                "citations": [],
                "original_text": ""
            }
    
    async def perform_reflection(
        self,
        research_topic: str,
        summaries: List[str],
        config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        反思研究完整性
        
        Args:
            research_topic: 研究主题
            summaries: 已有的研究总结
            config: 运行配置
            
        Returns:
            反思结果，包含是否需要更多研究
        """
        try:
            prompt = self._build_reflection_prompt(research_topic, summaries)
            
            response_data = await self.glm_client.analyze_with_web_search(prompt, temperature=0.5)
            
            if not response_data.get('success'):
                logger.warning(f"GLM反思失败: {response_data.get('error')}")
                return {
                    "is_sufficient": True,
                    "knowledge_gap": "",
                    "follow_up_queries": []
                }
            
            response = response_data.get('content', '')
            
            # 尝试解析JSON响应
            try:
                if response and isinstance(response, str):
                    if "```json" in response:
                        json_start = response.find("```json") + 7
                        json_end = response.find("```", json_start)
                        json_str = response[json_start:json_end].strip()
                    else:
                        json_str = response.strip()
                    
                    parsed = json.loads(json_str)
                    return {
                        "is_sufficient": parsed.get("is_sufficient", True),
                        "knowledge_gap": parsed.get("knowledge_gap", ""),
                        "follow_up_queries": parsed.get("follow_up_queries", [])
                    }
            except (json.JSONDecodeError, AttributeError):
                logger.warning("解析GLM反思JSON失败，假设研究充分")
            
            # 默认认为研究充分
            return {
                "is_sufficient": True,
                "knowledge_gap": "",
                "follow_up_queries": []
            }
            
        except Exception as e:
            logger.error(f"GLM反思失败: {e}")
            return {
                "is_sufficient": True,
                "knowledge_gap": "",
                "follow_up_queries": []
            }
    
    async def finalize_analysis(
        self,
        research_topic: str,
        summaries: List[str],
        sources: List[Dict[str, Any]],
        config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        完成最终分析
        
        Args:
            research_topic: 研究主题
            summaries: 研究总结列表
            sources: 来源列表
            config: 运行配置
            
        Returns:
            最终分析结果
        """
        try:
            prompt = self._build_final_analysis_prompt(research_topic, summaries)
            
            response_data = await self.glm_client.analyze_with_web_search(prompt, temperature=0.7)
            
            if response_data.get('success'):
                response = response_data.get('content', '')
                return {
                    "analysis": response or "分析完成，但未能生成具体内容。",
                    "confidence": 0.8,
                    "sources_count": len(sources),
                    "summary_count": len(summaries)
                }
            else:
                logger.warning(f"GLM最终分析失败: {response_data.get('error')}")
                return {
                    "analysis": f"基于收集的信息，{research_topic} 的分析结果需要进一步完善。",
                    "confidence": 0.5,
                    "sources_count": len(sources),
                    "summary_count": len(summaries)
                }
            
        except Exception as e:
            logger.error(f"GLM最终分析失败: {e}")
            return {
                "analysis": f"基于收集的信息，{research_topic} 的分析结果需要进一步完善。",
                "confidence": 0.5,
                "sources_count": len(sources),
                "summary_count": len(summaries)
            }
    
    def _get_default_queries(self, news_title: str, num_queries: int) -> List[Dict[str, str]]:
        """获取默认查询"""
        return [
            {
                "query": f"{news_title} 市场影响分析",
                "rationale": "分析新闻对相关市场的具体影响"
            },
            {
                "query": f"{news_title} 行业背景研究",
                "rationale": "研究新闻涉及的行业背景和发展趋势"
            }
        ][:num_queries]

    def _build_query_generation_prompt(
        self,
        news_title: str,
        news_content: str,
        num_queries: int
    ) -> str:
        """构建基于四层思维链的查询生成提示词"""
        return f"""
你是一位顶级投资研究分析师，请基于以下新闻内容，运用"四层思维链框架"生成{num_queries}个深度研究查询。

新闻标题：{news_title}

新闻内容：{news_content}

# 四层思维链查询策略

请按照以下思维层次生成查询，确保能够挖掘出"信息差"和"专业认知"：

## 第一层查询（事件感知）- 1个查询
- 关注事件的基本事实、时间线和直接影响
- 了解市场的普遍反应和大众认知

## 第二层查询（深度挖掘）- {max(1, num_queries-2)}个查询
- **供应链关系**：挖掘受影响主体的全球供应链角色
- **贸易数据**：查找具体的贸易依存关系和数据
- **产业链分析**：识别上下游影响和传导路径
- **替代性分析**：评估供应中断的替代方案和成本

## 第三层查询（国内影响）- 1个查询
- 聚焦国内相关行业的供需变化
- 分析价格传导机制和受益/受损企业

## 查询生成要求：
1. **信息差导向**：重点挖掘不为大众所知的深层信息
2. **数据具体化**：查询应该能获得具体的数据和量化信息
3. **关联性挖掘**：深入挖掘事件背后的关联关系
4. **投资导向**：最终要能指向具体的投资标的

请以JSON格式返回：
```json
{{
    "queries": [
        {{
            "query": "具体的搜索查询",
            "rationale": "为什么这个查询有助于深入理解新闻",
            "layer": "第X层",
            "focus": "供应链/贸易数据/产业链/国内影响等"
        }}
    ]
}}
```

注意：查询应该具体明确，避免泛泛而谈，要能够获得可操作的投资信息。
"""

    def _build_web_search_prompt(self, search_query: str) -> str:
        """构建网络搜索提示词"""
        return f"""
请帮我搜索和分析关于"{search_query}"的最新信息。

请提供：
1. 相关的背景信息和最新发展
2. 对市场和行业的影响分析
3. 相关数据和事实
4. 专业的观点和评估

请确保信息准确、专业，并具有分析价值。
"""

    def _build_reflection_prompt(self, research_topic: str, summaries: List[str]) -> str:
        """构建反思提示词"""
        summaries_text = "\n".join([f"- {summary}" for summary in summaries])

        return f"""
请评估关于"{research_topic}"的研究是否充分完整。

当前研究总结：
{summaries_text}

请分析：
1. 当前研究是否已经充分回答了关于该主题的关键问题？
2. 还有哪些重要方面需要进一步研究？
3. 如果需要更多研究，应该关注哪些具体问题？

请以JSON格式返回：
```json
{{
    "is_sufficient": true/false,
    "knowledge_gap": "如果不充分，描述缺少什么信息",
    "follow_up_queries": ["后续查询1", "后续查询2"]
}}
```
"""

    def _build_final_analysis_prompt(self, research_topic: str, summaries: List[str]) -> str:
        """构建基于四层思维链的最终分析提示词"""
        summaries_text = "\n".join([f"{i+1}. {summary}" for i, summary in enumerate(summaries)])

        return f"""
你是一位顶级的投资研究分析师，请基于以下研究总结，运用"四层思维链框架"为"{research_topic}"撰写一份深度投资分析报告。

研究总结：
{summaries_text}

# 四层思维链分析框架

## 第一层：事件感知与直接联想 (宏观表层) - "发生了什么？"

### 1.1 事件核心要素提取
- 明确识别新闻事件的核心内容
- 提取关键时间、地点、主体、影响范围
- 识别事件的紧急程度和市场关注度

### 1.2 第一性原理推断
- 基于常识和直接逻辑进行初步判断
- 识别最直观的市场影响路径
- 分析大众可能的第一反应和认知

### 1.3 大众认知预期
- 预测市场的普遍反应和热点标的
- 识别可能被过度关注的领域
- 评估"大众认知"的局限性和盲点

## 第二层：深挖供应链与关键信息 (宏观深层) - "真正影响了谁？"

### 2.1 核心受影响主体识别
- 深入分析事件直接冲击的国家、地区或行业
- 识别被忽视但实际受重大影响的主体
- 分析影响的传导路径和时间序列

### 2.2 全球供应链角色解构
- 分析受影响主体在全球经济中的具体角色
- 识别其主要产品、服务和出口商品
- 挖掘不为大众所知的关键商品或服务

### 2.3 贸易伙伴关系追溯
- 识别主要贸易伙伴和依赖关系
- 量化贸易依存度和替代难度
- 分析供应中断的连锁反应

### 2.4 影响程度量化分析
- 计算供应中断对下游市场的具体影响比例
- 评估替代方案的可行性和成本
- 识别供需缺口的规模和持续时间

## 第三层：聚焦国内产业与市场动态 (中观层面) - "国内谁会因此受益/受损？"

### 3.1 国内受冲击行业识别
- 精确定位受外部冲击影响的国内行业
- 分析影响的直接性和间接性
- 评估行业内部的分化程度

### 3.2 供需变化与价格走势推演
- **供给端分析**：量化供给缺口或过剩
- **需求端分析**：评估需求变化的弹性
- **价格预期**：基于供需关系预测价格走势
- **时间框架**：区分短期、中期、长期影响

### 3.3 产业链利益传导分析
- **受益方识别**：谁将从价格变化中获利
- **受损方识别**：谁将承担成本上升压力
- **利润再分配**：分析行业内利润的重新分配
- **竞争格局变化**：评估市场份额的潜在变化

## 第四层：筛选与锁定具体上市公司 (微观层面) - "最终买哪一个？"

### 4.1 初步标的筛选
- 基于前三层分析，筛选相关上市公司
- 建立候选标的池
- 进行初步的业务相关性评估

### 4.2 精准画像与纯度分析
- **业务占比分析**：相关业务在总收入中的占比
- **产能规模评估**：在行业中的地位和市场份额
- **业绩弹性测算**：价格变化对业绩的敏感性分析
- **财务健康度**：债务水平、现金流、盈利能力

### 4.3 投资标的排序
- 建立综合评分体系
- 考虑流动性、估值水平、技术面等因素
- 提供明确的投资优先级排序

### 4.4 风险收益评估
- 量化潜在收益空间
- 识别主要风险因素
- 提供风险调整后的收益预期

# 输出要求

请严格按照以上四层框架进行分析，每一层都要有具体的分析内容，特别要注意：

1. **信息差挖掘**：重点挖掘第二层的"专业认知"信息，这是获得超额收益的关键
2. **逻辑链条完整**：确保从宏观事件到微观标的的逻辑链条清晰完整
3. **量化分析**：尽可能提供具体的数据和量化分析
4. **可操作性**：最终要给出明确的投资建议和操作指导

请直接返回完整的分析报告，使用清晰的标题结构，确保每一层分析都有实质内容。
"""
