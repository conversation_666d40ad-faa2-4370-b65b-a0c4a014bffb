"""
深度研究引擎核心

迁移并简化deepreaserch的核心逻辑，专门用于新闻深度分析
"""

import logging
import asyncio
import uuid
from typing import Dict, Any, List, Optional, AsyncGenerator, Callable
from datetime import datetime

from ..adapters.gemini_adapter import GeminiResearchAdapter
from ..adapters.glm_adapter import GLMResearchAdapter
from ..adapters.news_adapter import NewsResearchAdapter
from .state_manager import (
    ResearchState,
    create_initial_research_state,
    ResearchConfiguration
)
from .data_manager import get_deep_research_data_manager
from ..analyzers.four_layer_analyzer import FourLayerThinkingAnalyzer

logger = logging.getLogger(__name__)


class DeepResearchEngine:
    """深度研究引擎"""
    
    def __init__(self):
        self.gemini_adapter = GeminiResearchAdapter()
        self.glm_adapter = GLMResearchAdapter()
        self.news_adapter = NewsResearchAdapter()
        self.config = ResearchConfiguration()
        self.data_manager = get_deep_research_data_manager()
        # 初始化四层思维链分析器（默认使用Gemini）
        self.four_layer_analyzer = FourLayerThinkingAnalyzer(
            self.gemini_adapter.llm_manager
        )
        
    async def analyze_news_deep(
        self,
        news_data: Dict[str, Any],
        analysis_config: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        对新闻进行深度分析
        
        Args:
            news_data: 新闻数据
            analysis_config: 分析配置
            
        Yields:
            分析进度和结果
        """
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 获取模型选择配置
            selected_model = analysis_config.get('model', 'gemini') if analysis_config else 'gemini'

            # 根据模型选择初始化四层分析器
            if selected_model == 'glm':
                self.four_layer_analyzer = FourLayerThinkingAnalyzer(
                    self.gemini_adapter.llm_manager, model_type='glm'
                )
                model_display_name = "GLM-4-Flash"
            else:
                self.four_layer_analyzer = FourLayerThinkingAnalyzer(
                    self.gemini_adapter.llm_manager, model_type='gemini'
                )
                model_display_name = "Gemini-2.0"

            yield {
                "type": "task_started",
                "task_id": task_id,
                "message": f"开始深度研究分析... (使用{model_display_name}模型)",
                "model": selected_model,
                "timestamp": datetime.now().isoformat()
            }
            
            # 简化实现，直接在方法调用中输出状态
            
            # 格式化研究上下文
            yield {
                "type": "context_analysis",
                "message": "分析新闻上下文和影响范围...",
                "timestamp": datetime.now().isoformat()
            }
            
            # 验证新闻内容质量和可信度
            title = news_data.get('title', '')
            content = news_data.get('content', '')
            source = news_data.get('source', '未知来源')
            
            # 基本内容验证
            content_quality_issues = []
            if len(title.strip()) < 10:
                content_quality_issues.append("新闻标题过短")
            if len(content.strip()) < 50:
                content_quality_issues.append("新闻内容过短")
            if '未知' in source or not source.strip():
                content_quality_issues.append("新闻来源不明")
            
            if content_quality_issues:
                yield {
                    "type": "content_quality_warning",
                    "message": f"检测到内容质量问题: {', '.join(content_quality_issues)}",
                    "details": content_quality_issues,
                    "timestamp": datetime.now().isoformat()
                }
            
            research_context = self.news_adapter.format_research_context(news_data)
            
            yield {
                "type": "context_completed",
                "message": "上下文分析完成",
                "context": research_context['analysis_context'],
                "content_quality": "存在质量问题" if content_quality_issues else "内容质量良好",
                "timestamp": datetime.now().isoformat()
            }
            
            # 创建初始研究状态
            initial_state = create_initial_research_state(
                news_data=news_data,
                config=analysis_config
            )
            
            # 步骤1: 生成研究查询
            yield {
                "type": "generating_queries",
                "message": "准备生成专门的研究查询...",
                "timestamp": datetime.now().isoformat()
            }
            
            # 提示用户API速率限制
            yield {
                "type": "rate_limit_info",
                "message": "⏱️ API速率限制：每次请求间隔10秒，确保连接稳定性，请耐心等待...",
                "details": "Gemini API限制为15次/分钟，我们采用10秒间隔策略确保最佳稳定性",
                "timestamp": datetime.now().isoformat()
            }
            
            yield {
                "type": "llm_call_starting",
                "message": f"🤖 准备调用{model_display_name}模型生成研究查询...",
                "model": model_display_name,
                "step": "查询生成",
                "timestamp": datetime.now().isoformat()
            }
            
            # 生成研究查询，增加重试机制
            queries = []
            async for status_update in self._generate_research_queries_with_retry(
                news_data, research_context, initial_state, selected_model
            ):
                yield status_update
                if status_update.get("type") == "queries_result":
                    queries = status_update.get("queries", [])
                    break
            
            yield {
                "type": "queries_generated",
                "message": f"✅ 已生成 {len(queries)} 个研究查询",
                "queries": queries,
                "timestamp": datetime.now().isoformat()
            }
            
            # 步骤2: 执行网络研究
            yield {
                "type": "web_research_started",
                "message": "🔍 开始网络搜索研究...",
                "total_queries": len(queries),
                "timestamp": datetime.now().isoformat()
            }
            
            research_results = []
            all_sources = []
            
            for i, query in enumerate(queries):
                yield {
                    "type": "search_progress",
                    "message": f"🔍 执行搜索查询 {i+1}/{len(queries)}: {query['query']}",
                    "current_query": query,
                    "progress": f"{i+1}/{len(queries)}",
                    "timestamp": datetime.now().isoformat()
                }
                
                yield {
                    "type": "llm_call_starting",
                    "message": f"🤖 调用AI模型进行网络搜索分析 ({i+1}/{len(queries)})...",
                    "model": "gemini-2.0-flash-exp",
                    "step": f"网络搜索 {i+1}",
                    "query": query['query'],
                    "timestamp": datetime.now().isoformat()
                }
                
                result = await self._perform_web_search_with_retry(
                    query['query'], task_id, i, selected_model
                )
                
                research_results.append(result)
                all_sources.extend(result.get('sources', []))
                
                yield {
                    "type": "search_completed",
                    "message": f"✅ 完成搜索查询 {i+1}/{len(queries)}",
                    "result_preview": result['text'][:200] + "..." if len(result['text']) > 200 else result['text'],
                    "timestamp": datetime.now().isoformat()
                }
            
            # 步骤3: 研究反思（可选）
            should_reflect = (
                len(research_results) > 0 and 
                initial_state.get('max_research_loops', 1) > 1
            )
            
            if should_reflect:
                yield {
                    "type": "reflection_started",
                    "message": "🤔 评估研究完整性...",
                    "timestamp": datetime.now().isoformat()
                }
                
                yield {
                    "type": "llm_call_starting",
                    "message": "🤖 调用AI模型评估研究质量...",
                    "model": "gemini-2.0-flash-exp",
                    "step": "研究反思",
                    "timestamp": datetime.now().isoformat()
                }
                
                reflection_result = await self._perform_reflection(
                    research_context['research_topic'],
                    [r['text'] for r in research_results]
                )
                
                # 如果需要更多信息，执行后续查询
                if not reflection_result['is_sufficient'] and reflection_result['follow_up_queries']:
                    yield {
                        "type": "additional_research",
                        "message": f"🔍 需要额外研究，执行 {len(reflection_result['follow_up_queries'])} 个补充查询",
                        "knowledge_gap": reflection_result['knowledge_gap'],
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    for idx, query in enumerate(reflection_result['follow_up_queries'][:2]):  # 限制补充查询数量
                        yield {
                            "type": "llm_call_starting",
                            "message": f"🤖 执行补充搜索 {idx+1}...",
                            "model": "gemini-2.0-flash-exp",
                            "step": f"补充搜索 {idx+1}",
                            "query": query,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        result = await self._perform_web_search(
                            query, task_id, len(research_results) + idx
                        )
                        research_results.append(result)
                        all_sources.extend(result.get('sources', []))
            
            # 步骤4: 四层思维链深度分析
            yield {
                "type": "four_layer_analysis_started",
                "message": "🧠 启动四层思维链深度分析...",
                "timestamp": datetime.now().isoformat()
            }

            yield {
                "type": "llm_call_starting",
                "message": "🤖 调用AI模型进行四层思维链分析...",
                "model": "gemini-2.0-flash-exp",
                "step": "四层思维链分析",
                "timestamp": datetime.now().isoformat()
            }

            try:
                # 使用四层思维链分析器
                four_layer_result = await self.four_layer_analyzer.analyze_news_event(
                    news_data,
                    [r['text'] for r in research_results]
                )

                yield {
                    "type": "four_layer_completed",
                    "message": "✅ 四层思维链分析完成",
                    "analysis_summary": four_layer_result.analysis_summary,
                    "investment_targets": four_layer_result.final_investment_targets,
                    "confidence": four_layer_result.overall_confidence,
                    "timestamp": datetime.now().isoformat()
                }

                # 将四层分析结果转换为最终分析格式
                final_analysis = {
                    "analysis": four_layer_result.analysis_summary,
                    "layer1_analysis": four_layer_result.layer1_result.analysis_content,
                    "layer2_analysis": four_layer_result.layer2_result.analysis_content,
                    "layer3_analysis": four_layer_result.layer3_result.analysis_content,
                    "layer4_analysis": four_layer_result.layer4_result.analysis_content,
                    "investment_targets": four_layer_result.final_investment_targets,
                    "confidence": four_layer_result.overall_confidence,
                    "analysis_type": "four_layer_thinking_chain"
                }

            except Exception as e:
                logger.error(f"四层思维链分析失败，回退到传统分析: {e}")
                yield {
                    "type": "four_layer_fallback",
                    "message": f"⚠️ 四层分析失败，使用传统分析方法: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }

                # 回退到传统分析方法
                final_analysis = await self._finalize_analysis_with_retry(
                    research_context['research_topic'],
                    [r['text'] for r in research_results],
                    all_sources
                )
            
            # 构建完整结果
            complete_result = {
                "task_id": task_id,
                "news_data": news_data,
                "research_topic": research_context['research_topic'],
                "analysis_context": research_context['analysis_context'],
                "queries_used": queries,
                "research_results": research_results,
                "final_analysis": final_analysis,
                "sources": all_sources,
                "citations": final_analysis.get('citations', []),
                "completed_at": datetime.now().isoformat()
            }
            
            # 保存分析结果到数据库
            yield {
                "type": "saving_results",
                "message": "💾 保存分析结果...",
                "timestamp": datetime.now().isoformat()
            }
            
            try:
                processing_time = int((datetime.now() - datetime.fromisoformat(
                    complete_result.get("completed_at", datetime.now().isoformat())
                )).total_seconds())
                
                analysis_id = self.data_manager.save_analysis_result(
                    task_id=task_id,
                    news_data=news_data,
                    research_topic=research_context['research_topic'],
                    queries=queries,
                    research_results=research_results,
                    final_analysis=final_analysis,
                    sources=all_sources,
                    analysis_context=research_context['analysis_context'],
                    status="completed",
                    processing_time=processing_time
                )
                
                complete_result["analysis_id"] = analysis_id
                
                yield {
                    "type": "results_saved",
                    "message": "✅ 分析结果已保存",
                    "analysis_id": analysis_id,
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as save_error:
                logger.error(f"保存分析结果失败: {save_error}")
                complete_result["save_error"] = str(save_error)
                
                yield {
                    "type": "save_warning",
                    "message": f"⚠️ 保存结果时出现问题: {str(save_error)}",
                    "timestamp": datetime.now().isoformat()
                }
            
            yield {
                "type": "analysis_completed",
                "message": "🎉 深度研究分析完成！",
                "result": complete_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"深度研究分析失败: {e}")
            
            # 保存错误状态到数据库
            try:
                self.data_manager.update_analysis_status(
                    task_id, "failed", str(e)
                )
            except Exception as save_error:
                logger.error(f"保存错误状态失败: {save_error}")
            
            yield {
                "type": "error",
                "message": f"❌ 分析过程中发生错误: {str(e)}",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _generate_research_queries_with_retry(
        self,
        news_data: Dict[str, Any],
        research_context: Dict[str, Any],
        state: ResearchState,
        model: str = 'gemini',
        max_retries: int = 3
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成研究查询（带重试机制）"""
        for attempt in range(max_retries):
            try:
                yield {
                    "type": "llm_processing",
                    "message": f"🔄 AI模型处理中... (尝试 {attempt + 1}/{max_retries})",
                    "timestamp": datetime.now().isoformat()
                }
                
                queries = await self._generate_research_queries(
                    news_data, research_context, state, model
                )
                if queries and len(queries) > 0:
                    yield {
                        "type": "llm_success",
                        "message": f"✅ AI模型调用成功，生成了 {len(queries)} 个查询",
                        "timestamp": datetime.now().isoformat()
                    }
                    yield {
                        "type": "queries_result",
                        "queries": queries,
                        "timestamp": datetime.now().isoformat()
                    }
                    return
                else:
                    logger.warning(f"查询生成结果为空，重试 {attempt + 1}/{max_retries}")
                    yield {
                        "type": "llm_retry",
                        "message": f"⚠️ 结果为空，准备重试 {attempt + 1}/{max_retries}",
                        "timestamp": datetime.now().isoformat()
                    }
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2 * (attempt + 1))  # 递增延迟
            except Exception as e:
                logger.error(f"查询生成失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                yield {
                    "type": "llm_error",
                    "message": f"❌ AI调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }
                if attempt < max_retries - 1:
                    yield {
                        "type": "llm_wait",
                        "message": f"⏱️ 等待 {2 * (attempt + 1)} 秒后重试...",
                        "timestamp": datetime.now().isoformat()
                    }
                    await asyncio.sleep(2 * (attempt + 1))  # 递增延迟
                else:
                    # 最后一次尝试失败，返回默认查询
                    title = news_data.get('title', '')
                    logger.warning("使用默认查询")
                    yield {
                        "type": "llm_fallback",
                        "message": "⚠️ AI调用失败，使用默认查询策略",
                        "timestamp": datetime.now().isoformat()
                    }
                    yield {
                        "type": "queries_result",
                        "queries": [
                            {
                                "query": f"{title} market impact analysis",
                                "rationale": "分析新闻对市场的影响（默认查询）"
                            },
                            {
                                "query": f"{title} industry background research",
                                "rationale": "研究新闻涉及的行业背景（默认查询）"
                            }
                        ],
                        "timestamp": datetime.now().isoformat()
                    }
                    return
        
        # 如果所有重试都失败，返回默认查询
        title = news_data.get('title', '')
        yield {
            "type": "llm_fallback",
            "message": "⚠️ 所有重试失败，使用备用查询方案",
            "timestamp": datetime.now().isoformat()
        }
        yield {
            "type": "queries_result",
            "queries": [
                {
                    "query": f"{title} analysis",
                    "rationale": "新闻分析（默认查询）"
                }
            ],
            "timestamp": datetime.now().isoformat()
        }

    async def _generate_research_queries(
        self,
        news_data: Dict[str, Any],
        research_context: Dict[str, Any],
        state: ResearchState,
        model: str = 'gemini'
    ) -> List[Dict[str, str]]:
        """生成研究查询"""
        try:
            # 使用新闻适配器生成基础查询
            news_queries = self.news_adapter.create_research_queries(
                news_data, 
                num_queries=state.get('initial_search_query_count', 2)
            )
            
            # 根据模型选择使用相应的适配器生成更精确的查询
            try:
                if model == 'glm':
                    adapter_queries = await self.glm_adapter.generate_research_queries(
                        news_data.get('content', ''),
                        news_data.get('title', ''),
                        num_queries=state.get('initial_search_query_count', 2)
                    )
                else:
                    adapter_queries = self.gemini_adapter.generate_research_queries(
                    news_data.get('content', ''),
                    news_data.get('title', ''),
                    num_queries=1
                )
                # 合并查询，优先使用新闻适配器的查询
                all_queries = news_queries + adapter_queries
                return all_queries[:state.get('initial_search_query_count', 2)]
            except Exception as e:
                model_name = "GLM" if model == 'glm' else "Gemini"
                logger.warning(f"{model_name}查询生成失败，使用默认查询: {e}")
                return news_queries
                
        except Exception as e:
            logger.error(f"查询生成失败: {e}")
            # 返回默认查询
            title = news_data.get('title', '')
            return [
                {
                    "query": f"{title} market impact analysis",
                    "rationale": "分析新闻对市场的影响"
                }
            ]
    
    async def _perform_web_search_with_retry(
        self,
        search_query: str,
        session_id: str,
        query_index: int,
        model: str = 'gemini',
        max_retries: int = 2
    ) -> Dict[str, Any]:
        """执行网络搜索（带重试机制）"""
        for attempt in range(max_retries):
            try:
                result = await self._perform_web_search(
                    search_query, session_id, query_index, model
                )
                # 检查结果质量
                if result and result.get('text') and len(result['text'].strip()) > 50:
                    return result
                else:
                    logger.warning(f"搜索结果质量不佳，重试 {attempt + 1}/{max_retries}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3 * (attempt + 1))  # 递增延迟
            except Exception as e:
                logger.error(f"网络搜索失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(3 * (attempt + 1))  # 递增延迟
        
        # 如果所有重试都失败，返回默认分析结果
        logger.warning(f"网络搜索彻底失败，为查询'{search_query}'生成默认分析")
        return {
            "text": f"基于现有知识对'{search_query}'的分析：这是一个重要的市场事件，需要关注其对相关行业和股票价格的影响。建议投资者密切关注后续发展并做好风险管理。",
            "sources": [],
            "citations": [],
            "original_text": ""
        }

    async def _perform_web_search(
        self,
        search_query: str,
        session_id: str,
        query_index: int,
        model: str = 'gemini'
    ) -> Dict[str, Any]:
        """执行网络搜索"""
        try:
            # 生成唯一的搜索ID
            search_id = hash(f"{session_id}_{query_index}")

            # 根据模型选择使用相应的适配器
            if model == 'glm':
                result = await self.glm_adapter.perform_web_search(
                    search_query,
                    search_id
                )
            else:
                result = self.gemini_adapter.perform_web_search(
                    search_query,
                    search_id
                )
            
            return result
            
        except Exception as e:
            logger.error(f"网络搜索失败: {e}")
            return {
                "text": f"搜索'{search_query}'时发生错误: {str(e)}",
                "sources": [],
                "citations": [],
                "original_text": ""
            }
    
    async def _perform_reflection(
        self,
        research_topic: str,
        summaries: List[str]
    ) -> Dict[str, Any]:
        """执行研究反思"""
        try:
            return self.gemini_adapter.perform_reflection(
                research_topic, summaries
            )
        except Exception as e:
            logger.error(f"研究反思失败: {e}")
            return {
                "is_sufficient": True,
                "knowledge_gap": "",
                "follow_up_queries": []
            }
    
    async def _finalize_analysis_with_retry(
        self,
        research_topic: str,
        summaries: List[str],
        sources: List[Dict[str, Any]],
        max_retries: int = 2
    ) -> Dict[str, Any]:
        """完成最终分析（带重试机制）"""
        for attempt in range(max_retries):
            try:
                result = await self._finalize_analysis(
                    research_topic, summaries, sources
                )
                # 检查分析结果质量
                if result and result.get('analysis') and len(result['analysis'].strip()) > 100:
                    return result
                else:
                    logger.warning(f"最终分析结果质量不佳，重试 {attempt + 1}/{max_retries}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(5 * (attempt + 1))  # 递增延迟
            except Exception as e:
                logger.error(f"最终分析生成失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5 * (attempt + 1))  # 递增延迟
        
        # 如果所有重试都失败，生成默认分析报告
        logger.warning("生成默认分析报告")
        default_analysis = self._generate_default_analysis(research_topic, summaries)
        return {
            "analysis": default_analysis,
            "sources": sources,
            "citations": []
        }

    def _generate_default_analysis(self, research_topic: str, summaries: List[str]) -> str:
        """生成默认分析报告"""
        analysis = f"""
## {research_topic} - 深度分析报告

### 重要声明
**本分析基于有限信息生成，部分原始资料可能存在质量问题或信息缺失。请谨慎对待分析结果。**

### 执行摘要
由于技术限制，无法获取充分的研究数据。建议投资者：
1. 通过权威媒体和官方渠道核实相关信息
2. 寻求专业投资顾问的建议
3. 在做出投资决策前进行独立研究

### 分析局限性
- 原始新闻信息可能存在可信度问题
- 缺乏足够的市场数据支撑
- 网络搜索结果有限
- 分析深度受到技术约束

### 研究结果汇总
"""
        
        if summaries:
            valid_summaries = [s for s in summaries if s and len(s.strip()) > 10]
            if valid_summaries:
                for i, summary in enumerate(valid_summaries[:2], 1):
                    analysis += f"\n{i}. {summary[:150]}..."
            else:
                analysis += "\n由于数据质量问题，无法提供详细研究结果。"
        else:
            analysis += "\n由于技术原因，无法获取研究数据。建议查阅权威财经媒体的相关报道。"
        
        analysis += """

### 风险提示与建议
**重要：本分析仅供参考，不构成投资建议。投资者应当：**
- 🔍 **信息核实**：通过多个权威渠道验证相关信息
- 📊 **独立研究**：进行充分的基本面和技术面分析
- 💼 **专业咨询**：寻求专业投资顾问的建议
- 🛡️ **风险管理**：根据个人风险承受能力做出投资决策

### 免责声明
本分析基于有限的公开信息，可能存在数据不完整、信息滞后等问题。投资有风险，决策需谨慎。
"""
        
        return analysis

    async def _finalize_analysis(
        self,
        research_topic: str,
        summaries: List[str],
        sources: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """完成最终分析"""
        try:
            return self.gemini_adapter.finalize_analysis(
                research_topic, summaries, sources
            )
        except Exception as e:
            logger.error(f"最终分析失败: {e}")
            return {
                "analysis": f"分析过程中发生错误: {str(e)}",
                "sources": [],
                "citations": []
            }
    
    def get_analysis_summary(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """获取分析摘要"""
        return {
            "task_id": result.get("task_id"),
            "news_title": result.get("news_data", {}).get("title", ""),
            "research_topic": result.get("research_topic", ""),
            "analysis_preview": result.get("final_analysis", {}).get("analysis", "")[:300] + "...",
            "sources_count": len(result.get("sources", [])),
            "queries_count": len(result.get("queries_used", [])),
            "completed_at": result.get("completed_at"),
            "priority_level": result.get("analysis_context", {}).get("analysis_priority", "medium")
        }
    
    async def batch_analyze_news(
        self,
        news_list: List[Dict[str, Any]],
        max_concurrent: int = 2
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """批量分析新闻"""
        
        yield {
            "type": "batch_started",
            "message": f"开始批量分析 {len(news_list)} 条新闻",
            "total_count": len(news_list)
        }
        
        # 创建分析任务
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def analyze_single_news(news_data: Dict[str, Any], index: int):
            async with semaphore:
                async for progress in self.analyze_news_deep(news_data):
                    # 为每个进度添加索引信息
                    progress["news_index"] = index
                    progress["news_title"] = news_data.get("title", "")
                    yield progress
        
        # 并发执行分析
        tasks = [
            analyze_single_news(news, i) 
            for i, news in enumerate(news_list)
        ]
        
        completed_count = 0
        
        for task in asyncio.as_completed(tasks):
            async for progress in await task:
                if progress.get("type") == "analysis_completed":
                    completed_count += 1
                    progress["batch_progress"] = {
                        "completed": completed_count,
                        "total": len(news_list),
                        "percentage": (completed_count / len(news_list)) * 100
                    }
                
                yield progress
        
        yield {
            "type": "batch_completed",
            "message": f"批量分析完成，共处理 {len(news_list)} 条新闻",
            "completed_count": completed_count
        } 