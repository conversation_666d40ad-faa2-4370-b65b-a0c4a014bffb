#!/usr/bin/env python3
"""
四层漏斗思维链分析演示
基于具体财经新闻案例，展示完整的投资研究报告生成过程
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../..'))

from backend.ai.deep_research.core.deep_research_engine import DeepResearchEngine

# 示例新闻数据 - 使用具体的财经事件
DEMO_NEWS_DATA = {
    "title": "欧盟宣布对俄罗斯钛金属实施全面制裁，航空航天产业面临供应链重构",
    "content": """
    欧盟委员会今日正式宣布，将对俄罗斯钛金属及相关产品实施全面制裁，禁止从俄罗斯进口钛金属原料和半成品。
    该制裁措施将于2024年1月1日正式生效，涉及钛海绵、钛锭、钛材等全产业链产品。
    
    俄罗斯是全球第二大钛金属生产国，占全球钛海绵产量的约18%，其中VSMPO-AVISMA公司是全球最大的钛材供应商之一，
    为波音、空客等航空巨头提供关键钛合金部件。制裁实施后，全球航空航天产业将面临严重的供应链中断风险。
    
    据行业分析师预测，钛金属价格可能在未来6-12个月内上涨30-50%，航空制造商将被迫寻找替代供应商，
    这将推动中国、日本等其他钛金属生产国的产能扩张和技术升级。
    
    波音公司发言人表示，公司正在评估制裁对生产计划的影响，并积极寻求多元化供应链解决方案。
    空客方面也确认，正在与其他钛材供应商洽谈长期合作协议。
    """,
    "source": "路透社",
    "publish_time": "2024-12-15 14:30:00",
    "url": "https://example.com/news/titanium-sanctions",
    "category": "国际财经",
    "importance": "高",
    "tags": ["制裁", "钛金属", "航空航天", "供应链", "俄罗斯", "欧盟"]
}

class FourLayerAnalysisDemo:
    """四层漏斗分析演示类"""
    
    def __init__(self):
        self.engine = DeepResearchEngine()
        
    async def run_demo_analysis(self):
        """运行演示分析"""
        print("=" * 80)
        print("🚀 四层漏斗思维链深度分析演示")
        print("=" * 80)
        print()
        
        print("📰 分析新闻:")
        print(f"标题: {DEMO_NEWS_DATA['title']}")
        print(f"来源: {DEMO_NEWS_DATA['source']}")
        print(f"时间: {DEMO_NEWS_DATA['publish_time']}")
        print()
        
        print("🔄 开始四层漏斗分析...")
        print("-" * 60)
        
        try:
            # 配置分析参数
            analysis_config = {
                "use_four_layer_analysis": True,
                "max_research_loops": 1,
                "initial_search_query_count": 3,
                "enable_detailed_logging": True
            }
            
            # 存储分析结果
            analysis_results = {
                "progress_logs": [],
                "layer_results": {},
                "final_result": None
            }
            
            # 执行分析
            async for result in self.engine.analyze_news_deep(
                news_data=DEMO_NEWS_DATA,
                analysis_config=analysis_config
            ):
                # 记录进度
                result_type = result.get("type", "unknown")
                message = result.get("message", "")
                timestamp = result.get("timestamp", datetime.now().isoformat())
                
                # 打印进度信息
                if result_type == "task_started":
                    print(f"✅ {message}")
                    print(f"   任务ID: {result.get('task_id')}")
                    
                elif result_type == "context_analysis":
                    print(f"🔍 {message}")
                    
                elif result_type == "generating_queries":
                    print(f"🤖 {message}")
                    queries = result.get("queries", [])
                    for i, query in enumerate(queries, 1):
                        print(f"   查询{i}: {query}")
                        
                elif result_type == "research_completed":
                    print(f"📚 {message}")
                    
                elif result_type == "four_layer_analysis_started":
                    print(f"🧠 {message}")
                    
                elif result_type == "llm_call_starting":
                    step = result.get("step", "未知步骤")
                    model = result.get("model", "未知模型")
                    print(f"🤖 {message} (模型: {model}, 步骤: {step})")
                    
                elif result_type == "four_layer_completed":
                    print(f"✅ {message}")
                    analysis_results["final_result"] = result
                    
                    # 显示分析摘要
                    print("\n" + "=" * 60)
                    print("📊 四层分析完成摘要")
                    print("=" * 60)
                    
                    analysis_summary = result.get("analysis_summary", "")
                    if analysis_summary:
                        print(analysis_summary)
                    
                    # 显示投资标的
                    investment_targets = result.get("investment_targets", [])
                    if investment_targets:
                        print("\n🎯 核心投资标的:")
                        for i, target in enumerate(investment_targets[:3], 1):
                            name = target.get("name", "未知公司")
                            symbol = target.get("symbol", "000000")
                            reason = target.get("recommendation_reason", "分析中")
                            confidence = target.get("confidence", 0.5)
                            print(f"{i}. {name}({symbol}) - 置信度: {confidence:.1%}")
                            print(f"   推荐理由: {reason[:100]}...")
                    
                    # 显示整体置信度
                    overall_confidence = result.get("confidence", 0.5)
                    print(f"\n📈 整体分析置信度: {overall_confidence:.1%}")
                    
                elif result_type == "error":
                    print(f"❌ 错误: {message}")
                    
                # 记录所有结果
                analysis_results["progress_logs"].append({
                    "type": result_type,
                    "message": message,
                    "timestamp": timestamp,
                    "data": result
                })
            
            # 保存分析结果到文件
            await self._save_analysis_results(analysis_results)
            
            print("\n" + "=" * 80)
            print("✅ 四层漏斗分析演示完成!")
            print("📁 详细结果已保存到: demo_analysis_results.json")
            print("=" * 80)
            
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    async def _save_analysis_results(self, results: Dict[str, Any]):
        """保存分析结果到文件"""
        try:
            # 创建结果文件
            output_file = "demo_analysis_results.json"
            
            # 添加元数据
            results["metadata"] = {
                "demo_version": "1.0",
                "analysis_time": datetime.now().isoformat(),
                "news_title": DEMO_NEWS_DATA["title"],
                "analysis_framework": "四层漏斗思维链模型"
            }
            
            # 保存到JSON文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
                
            print(f"📁 分析结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

async def main():
    """主函数"""
    print("🎯 四层漏斗思维链分析演示程序")
    print("基于具体财经新闻案例，展示完整的投资研究报告生成过程")
    print()
    
    demo = FourLayerAnalysisDemo()
    await demo.run_demo_analysis()

if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
