#!/usr/bin/env python3
"""
增强版四层漏斗思维链分析演示
确保输出完全符合用户的详细质量要求，避免空洞表述和占位符
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../..'))

from backend.ai.llm import LLMManager
from backend.ai.deep_research.analyzers.enhanced_four_layer_analyzer import EnhancedFourLayerAnalyzer

# 示例新闻数据 - 钛材制裁事件
DEMO_NEWS_DATA = {
    "title": "欧盟宣布对俄罗斯钛金属实施全面制裁，航空航天产业面临供应链重构",
    "content": """
    欧盟委员会今日正式宣布，将对俄罗斯钛金属及相关产品实施全面制裁，禁止从俄罗斯进口钛金属原料和半成品。
    该制裁措施将于2024年1月1日正式生效，涉及钛海绵、钛锭、钛材等全产业链产品。
    
    俄罗斯是全球第二大钛金属生产国，占全球钛海绵产量的约18%，其中VSMPO-AVISMA公司是全球最大的钛材供应商之一，
    为波音、空客等航空巨头提供关键钛合金部件。制裁实施后，全球航空航天产业将面临严重的供应链中断风险。
    
    据行业分析师预测，钛金属价格可能在未来6-12个月内上涨30-50%，航空制造商将被迫寻找替代供应商，
    这将推动中国、日本等其他钛金属生产国的产能扩张和技术升级。
    
    波音公司发言人表示，公司正在评估制裁对生产计划的影响，并积极寻求多元化供应链解决方案。
    空客方面也确认，正在与其他钛材供应商洽谈长期合作协议。
    """,
    "source": "路透社",
    "publish_time": "2024-12-15 14:30:00",
    "url": "https://example.com/news/titanium-sanctions",
    "category": "国际财经",
    "importance": "高",
    "tags": ["制裁", "钛金属", "航空航天", "供应链", "俄罗斯", "欧盟"]
}

# 模拟研究资料
RESEARCH_SUMMARIES = [
    "全球钛材市场规模约450亿美元，年增长率5-7%，航空航天领域占比45%",
    "中国钛材产能占全球35%，主要企业包括宝钛股份、西部材料等",
    "俄罗斯VSMPO-AVISMA公司为波音提供约40%的钛材需求",
    "钛材价格历史上受供应冲击影响显著，2008年金融危机后价格波动达60%",
    "航空级钛合金技术壁垒较高，认证周期通常需要2-3年"
]

class EnhancedFourLayerDemo:
    """增强版四层漏斗分析演示类"""
    
    def __init__(self):
        self.llm_manager = LLMManager()
        self.analyzer = EnhancedFourLayerAnalyzer(self.llm_manager)
        
    async def run_comprehensive_analysis(self):
        """运行综合分析演示"""
        print("=" * 100)
        print("🚀 增强版四层漏斗思维链深度分析演示")
        print("确保输出完全符合质量要求，避免空洞表述和占位符")
        print("=" * 100)
        print()
        
        print("📰 分析新闻:")
        print(f"标题: {DEMO_NEWS_DATA['title']}")
        print(f"来源: {DEMO_NEWS_DATA['source']}")
        print(f"时间: {DEMO_NEWS_DATA['publish_time']}")
        print(f"重要性: {DEMO_NEWS_DATA['importance']}")
        print()
        
        print("📚 研究资料:")
        for i, summary in enumerate(RESEARCH_SUMMARIES, 1):
            print(f"{i}. {summary}")
        print()
        
        print("🔄 开始增强版四层漏斗分析...")
        print("-" * 80)
        
        try:
            # 生成综合投资研究报告
            print("🧠 正在生成综合投资研究报告...")
            report = self.analyzer.generate_comprehensive_report(
                news_data=DEMO_NEWS_DATA,
                research_summaries=RESEARCH_SUMMARIES
            )
            
            # 显示分析结果
            await self._display_comprehensive_report(report)
            
            # 保存报告到文件
            await self._save_comprehensive_report(report)
            
            print("\n" + "=" * 100)
            print("✅ 增强版四层漏斗分析演示完成!")
            print("📁 详细报告已保存到: enhanced_analysis_report.json")
            print("=" * 100)
            
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    async def _display_comprehensive_report(self, report):
        """显示综合报告"""
        print("\n" + "=" * 80)
        print("📊 综合投资研究报告")
        print("=" * 80)
        
        # 显示执行摘要
        print("\n🎯 执行摘要:")
        print(report.executive_summary)
        
        # 显示核心推荐
        print("\n💎 核心推荐标的:")
        for i, rec in enumerate(report.core_recommendations, 1):
            print(f"{i}. {rec.get('name', '未知')}({rec.get('symbol', '000000')})")
            print(f"   当前价格: {rec.get('current_price', '待查询')}")
            print(f"   预期收益: {rec.get('expected_return', '待评估')}")
            print(f"   时间框架: {rec.get('time_frame', '待确定')}")
            print(f"   置信度: {rec.get('confidence', 0.5):.1%}")
            print(f"   推荐理由: {rec.get('recommendation_reason', '分析中')[:100]}...")
            print()
        
        # 显示备选推荐
        if report.backup_recommendations:
            print("🔄 备选推荐标的:")
            for i, backup in enumerate(report.backup_recommendations, 1):
                print(f"{i}. {backup.get('name', '未知')}({backup.get('symbol', '000000')})")
                print(f"   业务相关性: {backup.get('business_relevance', '待评估')}")
                print(f"   市值: {backup.get('market_cap', '待查询')}")
                print()
        
        # 显示风险评估
        print("⚠️ 风险评估:")
        risk = report.risk_assessment
        print(f"整体风险等级: {risk.get('overall_risk_level', '中等风险')}")
        print("主要风险因素:")
        for risk_item in risk.get('main_risks', []):
            print(f"  • {risk_item}")
        print()
        
        # 显示投资时间线
        print("📅 投资时间线:")
        timeline = report.investment_timeline
        for period, action in timeline.items():
            print(f"  {period}: {action}")
        print()
        
        # 显示整体置信度
        print(f"📈 整体分析置信度: {report.overall_confidence:.1%}")
        print(f"📅 报告生成时间: {report.generated_at}")
    
    async def _save_comprehensive_report(self, report):
        """保存综合报告到文件"""
        try:
            # 转换为可序列化的字典
            report_dict = {
                "metadata": {
                    "report_type": "增强版四层漏斗思维链分析",
                    "news_title": DEMO_NEWS_DATA["title"],
                    "analysis_time": report.generated_at,
                    "overall_confidence": report.overall_confidence
                },
                "executive_summary": report.executive_summary,
                "layer_analyses": {
                    "layer1": {
                        "name": report.layer1_analysis.layer_name,
                        "content": report.layer1_analysis.analysis_content,
                        "key_findings": report.layer1_analysis.key_findings,
                        "confidence": report.layer1_analysis.confidence_score,
                        "quantified_data": report.layer1_analysis.quantified_data,
                        "investment_insights": report.layer1_analysis.investment_insights
                    },
                    "layer2": {
                        "name": report.layer2_analysis.layer_name,
                        "content": report.layer2_analysis.analysis_content,
                        "key_findings": report.layer2_analysis.key_findings,
                        "confidence": report.layer2_analysis.confidence_score,
                        "quantified_data": report.layer2_analysis.quantified_data,
                        "investment_insights": report.layer2_analysis.investment_insights
                    },
                    "layer3": {
                        "name": report.layer3_analysis.layer_name,
                        "content": report.layer3_analysis.analysis_content,
                        "key_findings": report.layer3_analysis.key_findings,
                        "confidence": report.layer3_analysis.confidence_score,
                        "quantified_data": report.layer3_analysis.quantified_data,
                        "investment_insights": report.layer3_analysis.investment_insights
                    },
                    "layer4": {
                        "name": report.layer4_analysis.layer_name,
                        "content": report.layer4_analysis.analysis_content,
                        "key_findings": report.layer4_analysis.key_findings,
                        "confidence": report.layer4_analysis.confidence_score,
                        "quantified_data": report.layer4_analysis.quantified_data,
                        "investment_insights": report.layer4_analysis.investment_insights
                    }
                },
                "recommendations": {
                    "core_recommendations": report.core_recommendations,
                    "backup_recommendations": report.backup_recommendations
                },
                "risk_assessment": report.risk_assessment,
                "investment_timeline": report.investment_timeline
            }
            
            # 保存到JSON文件
            output_file = "enhanced_analysis_report.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_dict, f, ensure_ascii=False, indent=2)
                
            print(f"📁 综合报告已保存到: {output_file}")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")

async def main():
    """主函数"""
    print("🎯 增强版四层漏斗思维链分析演示程序")
    print("基于具体财经新闻案例，生成完整的投资研究报告")
    print("确保所有输出都有实质内容，避免占位符和空洞表述")
    print()
    
    demo = EnhancedFourLayerDemo()
    await demo.run_comprehensive_analysis()

if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
