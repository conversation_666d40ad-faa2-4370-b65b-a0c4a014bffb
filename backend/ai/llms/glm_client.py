# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import json
import logging
import asyncio
import traceback
from typing import Dict, List, Any, Optional
import httpx
from datetime import datetime

logger = logging.getLogger(__name__)

class GLMClient:
    """GLM-4-Flash API客户端，用于新闻影响分析"""
    
    def __init__(self):
        self.api_key = os.getenv('GLM_API_KEY')
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        self.model = "glm-4-flash"
        
        if not self.api_key:
            logger.warning("GLM_API_KEY环境变量未设置，新闻影响分析功能将不可用")
            
    def is_available(self) -> bool:
        """检查GLM API是否可用"""
        return bool(self.api_key)
    
    async def analyze_news_impact(self, news_title: str, news_content: str) -> Dict[str, Any]:
        """
        分析新闻对金融市场的影响
        
        Args:
            news_title: 新闻标题
            news_content: 新闻内容
            
        Returns:
            分析结果字典
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'GLM API不可用，请检查GLM_API_KEY环境变量'
            }
        
        try:
            # 构建分析提示词
            prompt = self._build_analysis_prompt(news_title, news_content)
            
            # 调用GLM API
            response = await self._call_glm_api(prompt)

            if response.get('success'):
                # 解析分析结果
                analysis_result = self._parse_analysis_result(response['content'])
                return {
                    'success': True,
                    'analysis': analysis_result,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', '分析失败')
                }
                
        except Exception as e:
            logger.error(f"新闻影响分析失败: {e}")
            return {
                'success': False,
                'error': f'分析过程中发生错误: {str(e)}'
            }

    async def analyze_with_web_search(self, prompt: str, temperature: float = 0.3) -> Dict[str, Any]:
        """
        使用网络搜索进行分析

        Args:
            prompt: 分析提示词
            temperature: 温度参数

        Returns:
            分析结果字典
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'GLM API不可用，请检查GLM_API_KEY环境变量'
            }

        try:
            logger.info("开始使用GLM模型进行网络搜索分析")

            # 调用GLM API，启用网络搜索
            response = await self._call_glm_api(prompt, enable_search=True)

            if response.get('success'):
                return {
                    'success': True,
                    'content': response['content'],
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', '搜索分析失败')
                }

        except Exception as e:
            logger.error(f"GLM网络搜索分析失败: {e}")
            return {
                'success': False,
                'error': f'搜索分析过程中发生错误: {str(e)}'
            }
    
    def _build_analysis_prompt(self, title: str, content: str) -> str:
        """构建基于第一性原理的分析提示词"""
        prompt = f"""
你是一位专业的金融分析师，请运用第一性原理方法分析以下新闻对金融市场的潜在影响。

【分析方法论：第一性原理框架】
第一性原理要求从最基本的经济规律出发，通过逻辑推导而非经验类比进行分析。

基础经济假设：
1. 市场参与者在信息约束下追求理性决策
2. 价格机制反映供需关系变化
3. 资本追求风险调整后的最大收益
4. 信息传播存在时滞和不对称性

分析框架：
第一步：事件本质分析 - 识别事件改变了哪些基础经济变量（供给、需求、成本、技术、政策等）
第二步：经济机制识别 - 确定影响传导的核心经济机制（价格机制、竞争机制、替代机制等）
第三步：传导路径分析 - 追踪影响在经济系统中的传播路径和时间序列
第四步：量化评估 - 基于供需弹性、市场结构等因素评估影响程度
第五步：风险因素识别 - 识别可能的反向因素和不确定性

【第一性原理分析示例】

示例1：供应约束事件的第一性原理分析
事件：上游关键材料供应商产能受限
第一性原理推导：
- 基础变量变化：市场供给曲线左移，供给弹性降低
- 经济机制：供给约束在需求不变情况下推高均衡价格，创造超额利润空间
- 传导路径：价格信号→激励替代供应商→具备产能的企业获得定价权和市场份额
- 量化评估：基于供需弹性系数和替代品可得性评估价格上涨幅度和持续时间
- 风险因素：原供应商产能恢复速度、新竞争者进入、需求侧变化
相关标的：具备替代生产能力的企业（基于生产函数和成本结构分析）

示例2：地缘风险事件的第一性原理分析
事件：地缘政治冲突升级
第一性原理推导：
- 基础变量变化：风险溢价上升，特定商品供给不确定性增加
- 经济机制：风险规避情绪推高避险资产需求，供给中断预期推高相关商品价格
- 传导路径：风险溢价→资产重新定价→相关产业链价值重估
- 量化评估：基于历史波动率和相关性系数评估风险溢价水平
- 风险因素：冲突持续时间、替代供给来源、政策干预可能性
相关标的：防务产业链企业（基于需求函数和产业集中度分析）

现在请运用第一性原理方法分析以下新闻：

新闻标题：{title}
新闻内容：{content}

分析要求：
1. 明确基础假设和适用条件
2. 识别事件改变的核心经济变量
3. 运用经济学基础理论进行逻辑推导
4. 建立清晰的因果关系链条：事件→基础影响→传导机制→最终结果
5. 每个推荐标的都要有理论依据和量化支撑
6. 避免基于历史经验的简单类比，强调逻辑推导的严谨性

请以JSON格式返回分析结果：

{{
    "overall_impact": {{
        "level": "高/中/低",
        "summary": "基于第一性原理的整体影响评估，说明核心经济机制"
    }},
    "us_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码2",
                "name": "公司名称2",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码3",
                "name": "公司名称3",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码4",
                "name": "公司名称4",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码5",
                "name": "公司名称5",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }}
        ],
        "analysis": "基于经济学理论的详细分析，包含传导机制和量化评估"
    }},
    "a_share_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码2",
                "name": "公司名称2",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码3",
                "name": "公司名称3",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码4",
                "name": "公司名称4",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码5",
                "name": "公司名称5",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }}
        ],
        "analysis": "基于经济学理论的详细分析，包含传导机制和量化评估"
    }},
    "hk_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码2",
                "name": "公司名称2",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码3",
                "name": "公司名称3",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码4",
                "name": "公司名称4",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }},
            {{
                "symbol": "股票代码5",
                "name": "公司名称5",
                "impact": "利多/利空",
                "reason": "基于第一性原理的推导：事件如何改变基础经济变量→通过何种经济机制传导→对企业价值创造的具体影响"
            }}
        ],
        "analysis": "基于经济学理论的详细分析，包含传导机制和量化评估"
    }},
    "major_indices": {{
        "sp500": {{"impact": "利多/利空/中性", "reason": "基于经济理论的影响机制说明"}},
        "nasdaq": {{"impact": "利多/利空/中性", "reason": "基于经济理论的影响机制说明"}},
        "shanghai_composite": {{"impact": "利多/利空/中性", "reason": "基于经济理论的影响机制说明"}},
        "shenzhen_component": {{"impact": "利多/利空/中性", "reason": "基于经济理论的影响机制说明"}},
        "hang_seng": {{"impact": "利多/利空/中性", "reason": "基于经济理论的影响机制说明"}}
    }},
    "key_stocks": [
        {{
            "symbol": "股票代码1",
            "name": "公司名称1",
            "market": "美股/A股/港股",
            "impact": "利多/利空",
            "impact_level": "高/中/低",
            "reason": "第一性原理分析：基础变量变化→经济机制→价值影响"
        }},
        {{
            "symbol": "股票代码2",
            "name": "公司名称2",
            "market": "美股/A股/港股",
            "impact": "利多/利空",
            "impact_level": "高/中/低",
            "reason": "第一性原理分析：基础变量变化→经济机制→价值影响"
        }},
        {{
            "symbol": "股票代码3",
            "name": "公司名称3",
            "market": "美股/A股/港股",
            "impact": "利多/利空",
            "impact_level": "高/中/低",
            "reason": "第一性原理分析：基础变量变化→经济机制→价值影响"
        }}
    ],
    "risk_assessment": {{
        "short_term_risk": "高/中/低",
        "medium_term_risk": "高/中/低",
        "key_risk_factors": ["基于理论分析的风险因素1", "基于理论分析的风险因素2"]
    }},
    "investment_advice": {{
        "strategy": "基于第一性原理分析的投资策略建议",
        "attention_points": ["理论支撑的注意点1", "理论支撑的注意点2"],
        "time_horizon": "短期/中期/长期"
    }}
}}

分析要求：
1. 运用第一性原理方法，从基础经济规律出发进行逻辑推导
2. 每个市场（美股、A股、港股）必须推荐5个相关股票，按照理论分析的相关性排序
3. 每个推荐标的的reason字段必须体现完整的第一性原理推导过程
4. 避免基于历史经验的简单类比，强调经济学理论支撑
5. 确保推荐的股票代码和公司名称准确无误，优先选择流动性好的股票
6. 所有分析结论都要有明确的理论依据和逻辑链条

请确保返回严格的JSON格式，reason字段必须体现第一性原理的逻辑推导过程。
"""
        return prompt
    
    async def _call_glm_api(self, prompt: str, enable_search: bool = False) -> Dict[str, Any]:
        """调用GLM API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 4000,
            "stream": False
        }

        # 如果启用搜索，添加web_search工具
        if enable_search:
            payload["tools"] = [
                {
                    "type": "web_search",
                    "web_search": {
                        "enable": True
                    }
                }
            ]
            logger.info("GLM API调用启用网络搜索功能")

        try:
            async with httpx.AsyncClient(timeout=120.0) as client:  # 增加超时时间以支持搜索
                response = await client.post(
                    self.base_url,
                    headers=headers,
                    json=payload
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    return {
                        'success': True,
                        'content': content
                    }
                else:
                    logger.error(f"GLM API调用失败: {response.status_code} - {response.text}")
                    return {
                        'success': False,
                        'error': f'API调用失败: {response.status_code}'
                    }

        except httpx.RequestError as e:
            logger.error(f"GLM API网络请求错误: {type(e).__name__} - {str(e)} - URL: {self.base_url}")
            return {
                'success': False,
                'error': f'网络请求错误: {str(e)}'
            }
        except Exception as e:
            logger.error(f"GLM API调用异常: {type(e).__name__} - {str(e)}")
            logger.error(f"完整异常信息:\n{traceback.format_exc()}")
            return {
                'success': False,
                'error': f'API调用异常: {str(e)}'
            }
    
    def _parse_analysis_result(self, content: str) -> Dict[str, Any]:
        """解析分析结果"""
        try:
            # 尝试直接解析JSON
            return json.loads(content)
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试提取JSON部分
            try:
                # 查找JSON开始和结束位置
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                
                if start_idx != -1 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    return json.loads(json_str)
                else:
                    raise ValueError("无法找到有效的JSON内容")
                    
            except Exception as e:
                logger.error(f"解析分析结果失败: {e}")
                # 返回默认结构
                return {
                    "overall_impact": {
                        "level": "中",
                        "summary": "分析结果解析失败，请重试"
                    },
                    "error": f"结果解析失败: {str(e)}",
                    "raw_content": content
                }

# 全局GLM客户端实例
_glm_client = None

def get_glm_client() -> GLMClient:
    """获取GLM客户端实例"""
    global _glm_client
    if _glm_client is None:
        _glm_client = GLMClient()
    return _glm_client 