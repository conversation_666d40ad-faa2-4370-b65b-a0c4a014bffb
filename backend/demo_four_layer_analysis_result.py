#!/usr/bin/env python3
"""
展示四层漏斗思维链分析的完整结果示例
演示如何查看和解析详细的分析内容
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.ai.deep_research.core.deep_research_engine import DeepResearchEngine

# 测试新闻数据
DEMO_NEWS_DATA = {
    "title": "印度宣布对中国稀土出口实施限制措施",
    "content": """
    印度政府今日宣布，将对向中国出口的稀土元素实施新的限制措施，
    包括镧、铈、钕等关键稀土元素。此举是为了保护印度的战略资源，
    并减少对中国稀土产业的依赖。印度是全球第二大稀土储量国，
    此次限制措施预计将影响全球稀土供应链，特别是新能源汽车、
    风力发电和电子产品制造行业。业内专家预计，这将推高稀土价格，
    并加速各国寻找替代供应源的步伐。
    """,
    "source": "印度时报",
    "publish_time": "2025-06-20 14:30:00",
    "category": "国际贸易"
}

async def demo_four_layer_analysis():
    """演示四层漏斗分析的完整结果"""
    
    print("🎯 四层漏斗思维链分析完整示例")
    print("=" * 80)
    print(f"📰 分析新闻：{DEMO_NEWS_DATA['title']}")
    print(f"📅 发布时间：{DEMO_NEWS_DATA['publish_time']}")
    print(f"📊 新闻来源：{DEMO_NEWS_DATA['source']}")
    print("=" * 80)
    
    try:
        # 创建深度研究引擎
        engine = DeepResearchEngine()
        
        # 配置分析参数
        analysis_config = {
            "use_four_layer_analysis": True,
            "max_research_loops": 1,
            "initial_search_query_count": 2,
            "enable_detailed_analysis": True
        }
        
        print("🔄 开始执行四层漏斗分析...")
        print()
        
        # 执行分析并收集完整结果
        complete_result = None
        four_layer_result = None

        async for result in engine.analyze_news_deep(
            news_data=DEMO_NEWS_DATA,
            analysis_config=analysis_config
        ):
            result_type = result.get("type", "unknown")
            message = result.get("message", "")

            # 只显示关键进度
            if result_type in ["task_started", "four_layer_analysis_started", "four_layer_completed", "analysis_completed"]:
                print(f"📍 {message}")

            # 保存四层分析结果
            if result_type == "four_layer_completed":
                four_layer_result = {
                    "analysis_summary": result.get("analysis_summary", ""),
                    "investment_targets": result.get("investment_targets", []),
                    "confidence": result.get("confidence", 0)
                }

            elif result_type == "analysis_completed":
                complete_result = result.get("result", {})
                break
        
        if complete_result or four_layer_result:
            print()
            print("=" * 80)
            print("📊 四层漏斗思维链分析详细结果")
            print("=" * 80)

            final_analysis = complete_result.get("final_analysis", {}) if complete_result else {}

            # 如果有四层分析结果，优先使用
            if four_layer_result:
                print("\n📋 四层分析总结")
                print("-" * 50)
                summary = four_layer_result.get("analysis_summary", "")
                if summary:
                    print(summary[:1200] + "..." if len(summary) > 1200 else summary)

                print("\n📈 投资标的推荐")
                print("-" * 50)
                targets = four_layer_result.get("investment_targets", [])
                if targets:
                    for i, target in enumerate(targets, 1):
                        print(f"{i}. {target.get('name', '')} ({target.get('symbol', '')})")
                        print(f"   类型: {target.get('target_type', '')}")
                        print(f"   推荐理由: {target.get('recommendation_reason', '')}")
                        print(f"   预期收益: {target.get('expected_return', '')}")
                        print(f"   时间框架: {target.get('time_frame', '')}")
                        print(f"   置信度: {target.get('confidence', 0):.1%}")
                        print()
                else:
                    print("暂无具体投资标的推荐")

                print(f"\n📊 整体置信度: {four_layer_result.get('confidence', 0):.1%}")

            # 如果有完整结果，显示详细的四层分析内容
            if final_analysis:
                # 显示第一层分析
                print("\n🔍 第一层：事件感知与直接联想")
                print("-" * 50)
                layer1_analysis = final_analysis.get("layer1_analysis", "")
                if layer1_analysis:
                    print(layer1_analysis[:800] + "..." if len(layer1_analysis) > 800 else layer1_analysis)

                # 显示第二层分析
                print("\n💡 第二层：深挖供应链与关键信息（信息差挖掘层）")
                print("-" * 50)
                layer2_analysis = final_analysis.get("layer2_analysis", "")
                if layer2_analysis:
                    print(layer2_analysis[:800] + "..." if len(layer2_analysis) > 800 else layer2_analysis)

                # 显示第三层分析
                print("\n🏭 第三层：聚焦国内产业与市场动态")
                print("-" * 50)
                layer3_analysis = final_analysis.get("layer3_analysis", "")
                if layer3_analysis:
                    print(layer3_analysis[:800] + "..." if len(layer3_analysis) > 800 else layer3_analysis)

                # 显示第四层分析
                print("\n🎯 第四层：筛选与锁定具体上市公司")
                print("-" * 50)
                layer4_analysis = final_analysis.get("layer4_analysis", "")
                if layer4_analysis:
                    print(layer4_analysis[:800] + "..." if len(layer4_analysis) > 800 else layer4_analysis)

                # 如果没有四层结果，显示完整结果中的投资标的
                if not four_layer_result:
                    print("\n📈 投资标的推荐")
                    print("-" * 50)
                    targets = final_analysis.get("investment_targets", [])
                    if targets:
                        for i, target in enumerate(targets, 1):
                            print(f"{i}. {target.get('name', '')} ({target.get('symbol', '')})")
                            print(f"   类型: {target.get('target_type', '')}")
                            print(f"   推荐理由: {target.get('recommendation_reason', '')}")
                            print(f"   预期收益: {target.get('expected_return', '')}")
                            print(f"   时间框架: {target.get('time_frame', '')}")
                            print(f"   置信度: {target.get('confidence', 0):.1%}")
                            print()
                    else:
                        print("暂无具体投资标的推荐")

                # 显示分析总结
                if not four_layer_result:
                    print("\n📋 分析总结")
                    print("-" * 50)
                    analysis_summary = final_analysis.get("analysis", "")
                    if analysis_summary:
                        print(analysis_summary[:1000] + "..." if len(analysis_summary) > 1000 else analysis_summary)

                # 显示整体评估
                print("\n📊 整体评估")
                print("-" * 50)
                confidence = final_analysis.get("confidence", 0)
                analysis_type = final_analysis.get("analysis_type", "")
                print(f"分析类型: {analysis_type}")
                if not four_layer_result:
                    print(f"整体置信度: {confidence:.1%}")
                if complete_result:
                    print(f"分析完成时间: {complete_result.get('completed_at', '')}")

                # 显示数据统计
                print("\n📈 分析数据统计")
                print("-" * 50)
                print(f"第一层分析长度: {len(layer1_analysis)} 字符")
                print(f"第二层分析长度: {len(layer2_analysis)} 字符")
                print(f"第三层分析长度: {len(layer3_analysis)} 字符")
                print(f"第四层分析长度: {len(layer4_analysis)} 字符")

                if complete_result:
                    targets = final_analysis.get("investment_targets", [])
                    print(f"投资标的数量: {len(targets)}")
                    print(f"研究查询数量: {len(complete_result.get('queries_used', []))}")
                    print(f"信息源数量: {len(complete_result.get('sources', []))}")
            
        else:
            print("❌ 未获得完整分析结果")
        
        print()
        print("=" * 80)
        print("✅ 四层漏斗思维链分析演示完成")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_analysis_framework():
    """打印分析框架说明"""
    
    framework = """
    🧠 四层漏斗思维链分析框架
    
    本框架采用"由外向内、由宏观到微观、由影响到标的"的系统性分析方法：
    
    📊 第一层：事件感知与直接联想
    ├─ 可信度评估（1-10分制，详细评分依据）
    ├─ 核心要素提取（时间、地点、主体、范围、紧急程度）
    ├─ 第一性原理推导（至少4条具体影响路径）
    ├─ 大众认知预期分析（识别"红海"领域）
    └─ 深挖方向指引（5个具体线索）
    
    🔍 第二层：深挖供应链与关键信息（信息差挖掘层）
    ├─ 核心受影响主体识别（包括被忽视的主体）
    ├─ 全球供应链角色解构（关注"不为人知的关键商品"）
    ├─ 贸易伙伴关系追溯（量化贸易依存度）
    ├─ 影响程度量化分析（数据、替代方案、时间）
    └─ 信息差发现总结（6个具体线索）
    
    🏭 第三层：聚焦国内产业与市场动态
    ├─ 国内受影响行业识别（直接+间接影响）
    ├─ 供需变化量化分析（供给端、需求端、平衡）
    ├─ 价格传导机制分析（具体百分比预测）
    └─ 公司筛选标准制定（受益方/受损方标准）
    
    🎯 第四层：筛选与锁定具体上市公司
    ├─ 候选标的池建立（A股、港股、美股各≥3个）
    ├─ 精准画像分析（业务占比、产能、弹性、财务）
    ├─ 综合评分体系（明确权重分配）
    └─ 风险收益评估（收益空间、风险因素、调整后预期）
    
    💡 核心优势：
    - 信息差挖掘：第二层是获得超额收益的关键
    - 逻辑完整性：从宏观事件到微观标的的完整链条
    - 避开红海：识别大众认知盲点，寻找蓝海机会
    - 精准筛选：多维度筛选最优投资标的
    """
    
    print(framework)

if __name__ == "__main__":
    print_analysis_framework()
    print()
    
    # 运行演示
    success = asyncio.run(demo_four_layer_analysis())
    
    if success:
        print("🎉 演示成功完成！")
        print("\n💡 使用建议：")
        print("1. 关注第二层的信息差挖掘，这是获得超额收益的关键")
        print("2. 重点分析第四层的投资标的，进行深度尽职调查")
        print("3. 结合整体置信度评估，制定合理的投资策略")
        print("4. 持续跟踪相关事件发展，动态调整投资组合")
        sys.exit(0)
    else:
        print("❌ 演示失败！")
        sys.exit(1)
