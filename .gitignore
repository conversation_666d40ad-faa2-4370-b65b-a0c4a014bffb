# Python-generated files
__pycache__/
*.py[oc]
build/
dist/
wheels/
*.egg-info
.coverage
agent_history.gif
static/browser_history/*.gif

# Virtual environments
.venv
venv/

# Environment variables
.env

# user conf
conf.yaml

.idea/
.langgraph_api/

# coverage report
coverage.xml
coverage/

# logs
*.log
*.pid

# backup files
*.backup*

# data
backend/data/financial_data.db
backend/data/financial_data.db-shm
backend/data/financial_data.db-wal
backend/data/divergence_data.db
data/divergence_data.db
frontend.zip
*.zip
*.db
